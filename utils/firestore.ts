import admin from 'firebase-admin';
import { cert, getApps, initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { v4 as uuidv4 } from 'uuid';

// Import our dual-database infrastructure
import { RepositoryManager } from '@/lib/repositories';

// Import services
import { OfficeHoursService } from '@/lib/services/office-hours';
import { LocationService } from '@/lib/services/locationService';

// Import models
import { Call } from '@/models/Call';
import { Client } from '@/models/Client';
import { Appointment } from '@/models/Appointment';
import { Location } from '@/models/Location';
import { CallDetail } from '@/models/CallDetail';
import { CalendarSlot, TimeSlot } from '@/models/CalendarSlot';
import { CallSession } from '@/models/CallSession';
import { CallType } from '@/models/CallTypes';
import { paginateQuery } from './firestore.utils';
import {
  PRIVACY_POLICY_URL,
  SUPPORT_TEAM_EMAIL,
  TERMS_OF_SERVICE_URL,
  TWO_FA_CODE_TTL_IN_SECONDS,
  TWO_FA_RESEND_CODE_RATE_LIMIT_IN_SECONDS,
  TWO_FA_TTL_IN_SECONDS,
} from '@/app-config';
import logger from '@/lib/external-api/v2/utils/logger';
import { AfterHoursCall } from '@/models/AfterHoursCall';
import { AfterHoursCallLog } from '@/models/AfterHoursCallLog';

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  try {
    const firebaseConfig = {
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    };

    if (!firebaseConfig.projectId || !firebaseConfig.clientEmail || !firebaseConfig.privateKey) {
      throw new Error(
        'Missing Firebase configuration. Please check FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY environment variables.',
      );
    }

    initializeApp({
      credential: cert(firebaseConfig),
    });

    console.log(
      '✅ Firebase Admin initialized successfully with project:',
      firebaseConfig.projectId,
    );
  } catch (error) {
    console.error('❌ Firebase Admin initialization error:', error);
    throw error;
  }
}

const db = getFirestore();
const repositoryManager = RepositoryManager.getInstance();

// Helper function to ensure database is initialized before use
async function ensureDatabaseInitialized(): Promise<void> {
  try {
    await repositoryManager.initialize();
  } catch (error) {
    logger.error(
      {
        context: 'ensureDatabaseInitialized',
        error: error instanceof Error ? error.message : String(error),
      },
      'Failed to initialize database',
    );
    throw error;
  }
}

// Collection references (for backward compatibility where needed)
const clientsCollection = db.collection('clients');
const callsCollection = db.collection('calls');
const appointmentsCollection = db.collection('appointments');
const locationsCollection = db.collection('locations');
const callDetailsCollection = db.collection('callDetails');
const calendarSlotsCollection = db.collection('availableCalendarSlots');
const callSessionsCollection = db.collection('callSessions');
const usersCollection = db.collection('users');
const staffCollection = db.collection('staff');
const emailCollection = db.collection('emails');
const emailTemplateCollection = db.collection('email-templates');
const otpCollection = db.collection('otp');

// Legacy converter functions - kept for backward compatibility where direct Firestore access is still needed
const convertToCallSession = (doc: admin.firestore.DocumentSnapshot): CallSession => {
  const data = doc.data();
  if (!data) {
    throw new Error(`CallSession document ${doc.id} has no data`);
  }

  return {
    id: doc.id,
    sessionId: data.sessionId || '',
    triggerEvent: data.triggerEvent || '',
    hasVoiceMail: data.hasVoiceMail || false,
    callType: data.callType || 0,
    callerPhone: data.callerPhone || '',
    patientId: data.patientId || '',
    agentId: data.agentId || '',
    appointmentId: data.appointmentId || '',
    isRedirected: data.isRedirected || false,
    callId: data.callId || '',
    status: data.status || '',
    createdAt: data.createdAt?.toDate() || new Date(),
    updatedAt: data.updatedAt?.toDate() || new Date(),
  };
};

const convertToCall = (doc: admin.firestore.DocumentSnapshot): Call => {
  const data = doc.data();
  if (!data) throw new Error(`Call with ID ${doc.id} not found`);

  // Convert date format - handle both Firestore timestamps and string dates
  let callDate = new Date();
  if (data.date) {
    if (data.date.toDate && typeof data.date.toDate === 'function') {
      // Handle Firestore timestamps
      callDate = data.date.toDate();
    } else if (typeof data.date === 'string') {
      // Handle ISO string dates
      try {
        callDate = new Date(data.date);
      } catch (e) {
        console.warn(`Failed to parse date string for call ${doc.id}:`, e);
      }
    } else if (data.date instanceof Date) {
      // Handle Date objects
      callDate = data.date;
    }
  }

  // Ensure clientId is a valid string
  const clientId =
    data.clientId && typeof data.clientId === 'string' && data.clientId.trim() !== ''
      ? data.clientId.trim()
      : '';

  return {
    id: doc.id,
    clientId: clientId,
    clientName: data.clientName || undefined,
    userId: data.userId || '',
    clinicId: data.clinicId || 0,
    locationId: data.locationId || 0,
    transferToLocationId: data.transferToLocationId || undefined,
    date: callDate,
    reason: data.reason || '',
    summary: data.summary || '',
    transcription: data.transcription || '',
    recordingUrl: data.recordingUrl || '',
    notes: data.notes || '',
    priorityScore: data.priorityScore || 0,
    urgent: data.urgent || false,
    tags: data.tags || [],
    phoneNumber: data.phoneNumber || '',
    sessionId: data.sessionId || '',
    hasVoiceMail: data.hasVoiceMail || false,
    isOutboundCall: data.isOutboundCall || false,
    voicemailUrl: data.voicemailUrl || '',
    transcriptionWithAudio: data.transcriptionWithAudio || '',
    duration: data.duration || '',
    // Legacy single call type field (latest)
    type: (() => {
      const raw = data.type;
      if (raw === undefined || raw === null) return 0;
      if (typeof raw === 'number') return raw;
      if (typeof raw === 'string') {
        if (/^\d+$/.test(raw)) return Number(raw);
        if (raw.toLowerCase() === 'other') return 0;
      }
      return 0;
    })(),
    // New multi-type history – fall back to single type array when missing
    callTypes: (() => {
      const raw = data.callTypes;
      if (Array.isArray(raw)) {
        return raw.map(v => (typeof v === 'number' ? v : Number(v))).filter(n => !isNaN(n));
      }
      // If legacy single value exists, seed array with it
      const single = (() => {
        const rawType = data.type;
        if (rawType === undefined || rawType === null) return 0;
        if (typeof rawType === 'number') return rawType;
        if (typeof rawType === 'string' && /^\d+$/.test(rawType)) return Number(rawType);
        return 0;
      })();
      return [single];
    })(),
  };
};

// Helper functions to convert Firestore data to our models
const convertToLocation = (doc: admin.firestore.DocumentSnapshot): Location => {
  const data = doc.data();
  if (!data) throw new Error(`Location with ID ${doc.id} not found`);

  let createdAt = new Date();
  if (data.createdAt) {
    if (data.createdAt.toDate && typeof data.createdAt.toDate === 'function') {
      createdAt = data.createdAt.toDate();
    } else if (typeof data.createdAt === 'string') {
      try {
        createdAt = new Date(data.createdAt);
      } catch (e) {
        console.warn(`Failed to parse createdAt date for location ${doc.id}:`, e);
      }
    } else if (data.createdAt instanceof Date) {
      createdAt = data.createdAt;
    }
  }

  let updatedAt = new Date();
  if (data.updatedAt) {
    if (data.updatedAt.toDate && typeof data.updatedAt.toDate === 'function') {
      updatedAt = data.updatedAt.toDate();
    } else if (typeof data.updatedAt === 'string') {
      try {
        updatedAt = new Date(data.updatedAt);
      } catch (e) {
        console.warn(`Failed to parse updatedAt date for location ${doc.id}:`, e);
      }
    } else if (data.updatedAt instanceof Date) {
      updatedAt = data.updatedAt;
    }
  }

  return {
    id: doc.id,
    clinicId: data.clinicId || 0,
    practiceId: data.practiceId || 'default-practice',
    name: data.name || '',
    address: data.address || '',
    phone: data.phone || '',
    timeZone: data.timeZone || '',
    isActive: data.isActive ?? true,
    practiceName: data.practiceName || '',
    officeHours: { ...(data.officeHours || {}) },
    createdAt,
    updatedAt,
  };
};

const convertToCallDetail = (doc: admin.firestore.DocumentSnapshot): CallDetail => {
  const data = doc.data();
  if (!data) throw new Error(`CallDetail with ID ${doc.id} not found`);

  let createdAt = new Date();
  if (data.createdAt) {
    if (data.createdAt.toDate && typeof data.createdAt.toDate === 'function') {
      createdAt = data.createdAt.toDate();
    } else if (typeof data.createdAt === 'string') {
      try {
        createdAt = new Date(data.createdAt);
      } catch (e) {
        console.warn(`Failed to parse createdAt date for call detail ${doc.id}:`, e);
      }
    } else if (data.createdAt instanceof Date) {
      createdAt = data.createdAt;
    }
  }

  let updatedAt = new Date();
  if (data.updatedAt) {
    if (data.updatedAt.toDate && typeof data.updatedAt.toDate === 'function') {
      updatedAt = data.updatedAt.toDate();
    } else if (typeof data.updatedAt === 'string') {
      try {
        updatedAt = new Date(data.updatedAt);
      } catch (e) {
        console.warn(`Failed to parse updatedAt date for call detail ${doc.id}:`, e);
      }
    } else if (data.updatedAt instanceof Date) {
      updatedAt = data.updatedAt;
    }
  }

  return {
    id: doc.id,
    callId: data.callId || '',
    summary: data.summary || '',
    voicemailSummary: data.voicemailSummary || '',
    transcription: data.transcription || '',
    transcriptionWithAudio: data.transcriptionWithAudio || '',
    createdAt,
    updatedAt,
  };
};

// Fix the CalendarSlot converter
const convertToCalendarSlot = (doc: admin.firestore.DocumentSnapshot): CalendarSlot => {
  const data = doc.data();
  if (!data) throw new Error(`Calendar slot with ID ${doc.id} not found`);

  return {
    id: doc.id,
    userId: data.userId || '',
    locationId: data.locationId || '',
    date: data.date || '',
    timeSlots: data.timeSlots || [],
  };
};

// Fix the Appointment converter
const convertToAppointment = (doc: admin.firestore.DocumentSnapshot): Appointment => {
  const data = doc.data();
  if (!data) throw new Error(`Appointment with ID ${doc.id} not found`);

  let createdAt = new Date();
  if (data.createdAt) {
    if (data.createdAt.toDate && typeof data.createdAt.toDate === 'function') {
      createdAt = data.createdAt.toDate();
    } else if (typeof data.createdAt === 'string') {
      try {
        createdAt = new Date(data.createdAt);
      } catch (e) {
        console.warn(`Failed to parse createdAt date for appointment ${doc.id}:`, e);
      }
    } else if (data.createdAt instanceof Date) {
      createdAt = data.createdAt;
    }
  }

  let updatedAt = new Date();
  if (data.updatedAt) {
    if (data.updatedAt.toDate && typeof data.updatedAt.toDate === 'function') {
      updatedAt = data.updatedAt.toDate();
    } else if (typeof data.updatedAt === 'string') {
      try {
        updatedAt = new Date(data.updatedAt);
      } catch (e) {
        console.warn(`Failed to parse updatedAt date for appointment ${doc.id}:`, e);
      }
    } else if (data.updatedAt instanceof Date) {
      updatedAt = data.updatedAt;
    }
  }

  return {
    id: doc.id,
    userId: data.userId || '',
    clientId: data.clientId || '',
    clientName: data.clientName || '',
    slotId: data.slotId || '',
    callId: data.callId || '',
    date: data.date || '',
    time: data.time || '',
    status: data.status || 'active',
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

// Helper function to normalize phone numbers for searching
const normalizePhoneNumber = (phoneNumber: string): string => {
  return phoneNumber.replace(/\D/g, ''); // Remove all non-digit characters
};

// Helper function to generate phone search variations
const generatePhoneSearchTokens = (phoneNumber: string): string[] => {
  const normalized = normalizePhoneNumber(phoneNumber);
  const tokens: string[] = [normalized];

  // Add variations: with +1, without +1, with/without area code, etc.
  if (normalized.length === 11 && normalized.startsWith('1')) {
    tokens.push(normalized.substring(1)); // Remove leading 1
  }
  if (normalized.length === 10) {
    tokens.push(`1${normalized}`); // Add leading 1
  }

  // Add partial matches (last 4, last 7 digits)
  if (normalized.length >= 4) {
    tokens.push(normalized.slice(-4));
  }
  if (normalized.length >= 7) {
    tokens.push(normalized.slice(-7));
  }

  return Array.from(new Set(tokens)); // Remove duplicates
};

export const otpService = {
  async saveOtp(email: string, code: string): Promise<string> {
    const key = `otp_${email}`;
    const expireAt = admin.firestore.Timestamp.fromDate(
      new Date(Date.now() + 1000 * TWO_FA_CODE_TTL_IN_SECONDS),
    );
    await otpCollection.doc(key).set({
      email,
      code,
      expireAt,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });
    return code;
  },

  async verifyOtp(email: string, code: string): Promise<boolean> {
    const key = `otp_${email}`;
    const otpDoc = await otpCollection.doc(key).get();
    if (!otpDoc.exists) {
      return false;
    }

    const data = otpDoc.data();
    const createdAt = data?.createdAt.toDate();
    if (!createdAt) {
      return false;
    }

    const now = new Date();
    const diff = now.getTime() - createdAt.getTime();
    const diffSeconds = diff / 1000;

    if (diffSeconds > TWO_FA_CODE_TTL_IN_SECONDS) {
      return false;
    }

    return data?.code === code;
  },

  /**
   * Checks if the user can send an OTP. Rate limited to 1 OTP per 1 minutes.
   * @param email - The email of the user.
   * @returns True if the user can send an OTP, false otherwise.
   */
  async canSendOtp(email: string): Promise<boolean> {
    const rateLimitInSeconds = TWO_FA_RESEND_CODE_RATE_LIMIT_IN_SECONDS;
    const key = `otp_${email}`;
    const otpDoc = await otpCollection.doc(key).get();
    if (!otpDoc.exists) {
      return true;
    }

    const data = otpDoc.data();
    const createdAt = data?.createdAt.toDate();
    if (!createdAt) {
      return true;
    }

    return createdAt.getTime() + 1000 * rateLimitInSeconds < Date.now();
  },

  async shouldDo2FA(email: string): Promise<boolean> {
    const key = `2fa_completed_${email}`;
    const docRef = otpCollection.doc(key);
    const docSnap = await docRef.get();
    if (!docSnap.exists) {
      return true;
    }

    const data = docSnap.data();
    const createdAt = data?.createdAt.toDate();
    if (!createdAt) {
      return true;
    }

    const now = new Date();
    const diff = now.getTime() - createdAt.getTime();
    const diffSeconds = diff / 1000;

    return diffSeconds > TWO_FA_TTL_IN_SECONDS;
  },

  async set2FACompleted(email: string): Promise<void> {
    const key = `2fa_completed_${email}`;
    await otpCollection.doc(key).set({
      email,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });
  },
};

// Firestore CRUD operations for Mail
export const mailService = {
  async sendTemplatedEmail(params: {
    to?: string[];
    toUids?: string[];
    from?: string;
    template: string;
    data?: Record<string, unknown>;
  }): Promise<string[]> {
    const mailIds: string[] = [];
    const {
      to: emailRecipients = [],
      toUids: uidRecipients = [],
      from = 'FrontDesk AI <<EMAIL>>',
      template,
      data,
    } = params;
    const templateDoc = await emailTemplateCollection.doc(template).get();
    if (!templateDoc.exists) {
      throw new Error(`Template with ID ${template} not found`);
    }

    const recipients = [
      ...emailRecipients.map(email => ({ to: email })),
      ...uidRecipients.map(uid => ({ toUids: [uid] })),
    ];

    if (!recipients.length) {
      return mailIds;
    }

    // Get a new write batch
    const batch = db.batch();
    for (const recipient of recipients) {
      const mailId = uuidv4();
      mailIds.push(mailId);
      const mailRef = emailCollection.doc(mailId);
      batch.set(mailRef, {
        ...recipient,
        from,
        template: {
          name: template,
          data: {
            supportTeamEmail: SUPPORT_TEAM_EMAIL,
            privacyPolicyUrl: PRIVACY_POLICY_URL,
            termsOfServiceUrl: TERMS_OF_SERVICE_URL,
            ...(data || {}),
          },
        },
      });
    }

    // Commit the batch
    await batch.commit();

    return mailIds;
  },

  async sendVerificationEmail(email: string, link: string) {
    const template = 'verify-email';
    const data = {
      link,
    };
    await this.sendTemplatedEmail({ to: [email], template, data });
  },

  async sendPasswordResetEmail(email: string, link: string) {
    const template = 'reset-password';
    const data = {
      link,
    };
    await this.sendTemplatedEmail({ to: [email], template, data });
  },

  async send2FAVerificationEmail(email: string, code: string, codeTtl: number) {
    const template = '2fa-verification';
    const data = {
      code,
      codeTtl,
    };
    await this.sendTemplatedEmail({ to: [email], template, data });
  },

  async sendNewAppointmentEmail(
    toUids: string[],
    appointment: {
      patientName?: string;
      doctorName?: string;
      startTime?: string;
      contactNumber?: string;
    },
  ) {
    const template = 'new-appointment';
    await this.sendTemplatedEmail({ toUids, template, data: appointment });
  },

  async sendNewCallEmail(
    toUids: string[],
    data: {
      ctaLink: string;
    },
  ) {
    const template = 'new-call';
    await this.sendTemplatedEmail({ toUids, template, data });
  },

  async sendNewVoiceMailEmail(
    toUids: string[],
    data: {
      ctaLink: string;
    },
  ) {
    const template = 'new-voice-mail';
    await this.sendTemplatedEmail({ toUids, template, data });
  },

  async sendDailyMonitoringEmail(
    toUids: string[],
    data: {
      ctaLink: string;
      reportDate: string;
      totalCalls: number;
      potentialIssuesCount: number;
      callTypeBreakdown: Record<string, number>;
    },
  ) {
    const template = 'daily-monitoring';
    await this.sendTemplatedEmail({ toUids, template, data });
  },

  async sendHourlyMonitoringEmail(
    toUids: string[],
    data: {
      ctaLink: string;
      reportDate: string;
      reportTime: string;
      totalCalls: number;
      potentialIssuesCalls: (Call & {
        summary: string;
        isPotentialIssue: boolean;
        callDetailsLink: string;
      })[];
      nonIssuesCalls: (Call & {
        summary: string;
        isPotentialIssue: boolean;
        callDetailsLink: string;
      })[];
    },
  ) {
    const template = 'hourly-monitoring';
    await this.sendTemplatedEmail({ toUids, template, data });
  },
};

// Clients Service - Updated to use dual-database repositories
export const clientsService = {
  async getAllClients(): Promise<Client[]> {
    try {
      await ensureDatabaseInitialized();
      const result = await repositoryManager.clients.findMany({});
      return result.items;
    } catch (error) {
      console.error('Error getting clients:', error);
      throw error;
    }
  },

  async findClientByNameAndBirthday(
    fullName: string,
    birthday: string | Date,
  ): Promise<Client | null> {
    try {
      await ensureDatabaseInitialized();
      const searchDate = typeof birthday === 'string' ? new Date(birthday) : birthday;
      const result = await repositoryManager.clients.findMany({
        where: { fullName, birthday: searchDate },
        limit: 1,
      });
      return result.items.length > 0 ? result.items[0] : null;
    } catch (error) {
      console.error('Error finding client by name and birthday:', error);
      throw error;
    }
  },

  async getClientById(id: string): Promise<Client | null> {
    try {
      if (!id || typeof id !== 'string' || id.trim() === '') {
        console.warn('Invalid client ID provided:', id);
        return null;
      }

      await ensureDatabaseInitialized();
      return await repositoryManager.clients.findById(id);
    } catch (error) {
      console.error('Error getting client:', error);
      throw error;
    }
  },

  async createClient(client: Omit<Client, 'id'>): Promise<Client> {
    try {
      await ensureDatabaseInitialized();
      const id = uuidv4();
      const newClient = { ...client, id };
      return await repositoryManager.clients.create(newClient);
    } catch (error) {
      console.error('Error creating client:', error);
      throw error;
    }
  },

  async updateClient(id: string, client: Partial<Client>): Promise<void> {
    try {
      await ensureDatabaseInitialized();
      await repositoryManager.clients.update(id, client);
    } catch (error) {
      console.error('Error updating client:', error);
      throw error;
    }
  },

  async deleteClient(id: string): Promise<void> {
    try {
      await ensureDatabaseInitialized();
      await repositoryManager.clients.delete(id);
    } catch (error) {
      console.error('Error deleting client:', error);
      throw error;
    }
  },
};

// Calls Service - Updated to use dual-database repositories
export const callsService = {
  async getAllCalls(): Promise<Call[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info({ context: 'callsService.getAllCalls' }, 'Getting all calls via MySQL');
      const result = await repositoryManager.calls.findMany({});
      logger.info(
        { context: 'callsService.getAllCalls', count: result.items.length },
        'Successfully retrieved calls via MySQL',
      );
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'callsService.getAllCalls',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get calls via MySQL',
      );
      throw error;
    }
  },

  /**
   * Diagnostic function to debug fetchAndFilterCalls issues
   * Tests different levels of data access to identify the problem
   */
  async diagnoseFetchIssues(): Promise<{
    totalCalls: number;
    callsWithPhoneNumbers: number;
    validPhoneNumbers: number;
    sampleCall?: Call;
    indexErrors?: string[];
  }> {
    const diagnosis = {
      totalCalls: 0,
      callsWithPhoneNumbers: 0,
      validPhoneNumbers: 0,
      sampleCall: undefined as Call | undefined,
      indexErrors: [] as string[],
    };

    try {
      logger.info({ context: 'diagnoseFetchIssues' }, 'Starting diagnostic...');

      // Test 1: Basic collection access
      logger.info({ context: 'diagnoseFetchIssues' }, 'Test 1: Basic collection access');
      const basicSnapshot = await callsCollection.limit(5).get();
      diagnosis.totalCalls = basicSnapshot.size;
      logger.info(
        { context: 'diagnoseFetchIssues', totalCalls: diagnosis.totalCalls },
        'Basic access result',
      );

      if (basicSnapshot.size > 0) {
        const firstDoc = basicSnapshot.docs[0];
        diagnosis.sampleCall = convertToCall(firstDoc);
        logger.info(
          { context: 'diagnoseFetchIssues', sampleCall: diagnosis.sampleCall },
          'Sample call data',
        );
      }

      // Test 2: Phone number filtering
      logger.info({ context: 'diagnoseFetchIssues' }, 'Test 2: Phone number analysis');
      for (const doc of basicSnapshot.docs) {
        const call = convertToCall(doc);
        if (call.phoneNumber) {
          diagnosis.callsWithPhoneNumbers++;
          if (call.phoneNumber.trim() !== '' && call.phoneNumber !== 'Unknown') {
            diagnosis.validPhoneNumbers++;
          }
        }
      }

      // Test 3: Try simple orderBy query
      try {
        logger.info({ context: 'diagnoseFetchIssues' }, 'Test 3: OrderBy query');
        const orderedSnapshot = await callsCollection.orderBy('date', 'desc').limit(3).get();
        logger.info(
          { context: 'diagnoseFetchIssues', orderedCount: orderedSnapshot.size },
          'OrderBy query result',
        );
      } catch (error) {
        const errorMsg = `OrderBy query failed: ${error instanceof Error ? error.message : String(error)}`;
        diagnosis.indexErrors.push(errorMsg);
        logger.error({ context: 'diagnoseFetchIssues', error: errorMsg }, 'OrderBy failed');
      }

      // Test 4: Try clinicId filter (if we have sample data)
      if (diagnosis.sampleCall?.clinicId) {
        try {
          logger.info({ context: 'diagnoseFetchIssues' }, 'Test 4: ClinicId filter');
          const clinicSnapshot = await callsCollection
            .where('clinicId', '==', diagnosis.sampleCall.clinicId)
            .limit(3)
            .get();
          logger.info(
            { context: 'diagnoseFetchIssues', clinicFilterCount: clinicSnapshot.size },
            'ClinicId filter result',
          );
        } catch (error) {
          const errorMsg = `ClinicId filter failed: ${error instanceof Error ? error.message : String(error)}`;
          diagnosis.indexErrors.push(errorMsg);
          logger.error(
            { context: 'diagnoseFetchIssues', error: errorMsg },
            'ClinicId filter failed',
          );
        }
      }

      // Test 5: Try compound query (the problematic one)
      if (diagnosis.sampleCall?.clinicId) {
        try {
          logger.info({ context: 'diagnoseFetchIssues' }, 'Test 5: Compound query');
          const compoundSnapshot = await callsCollection
            .where('clinicId', '==', diagnosis.sampleCall.clinicId)
            .orderBy('date', 'desc')
            .limit(3)
            .get();
          logger.info(
            { context: 'diagnoseFetchIssues', compoundCount: compoundSnapshot.size },
            'Compound query result',
          );
        } catch (error) {
          const errorMsg = `Compound query failed: ${error instanceof Error ? error.message : String(error)}`;
          diagnosis.indexErrors.push(errorMsg);
          logger.error(
            { context: 'diagnoseFetchIssues', error: errorMsg },
            'Compound query failed',
          );
        }
      }

      logger.info({ context: 'diagnoseFetchIssues', diagnosis }, 'Diagnostic complete');
      return diagnosis;
    } catch (error) {
      logger.error(
        {
          context: 'diagnoseFetchIssues',
          error: error instanceof Error ? error.message : String(error),
        },
        'Diagnostic failed',
      );
      throw error;
    }
  },

  /**
   * Diagnostic function specifically for investigating date-related pagination issues
   */
  async diagnoseDatePaginationIssue(params: {
    clinicId?: number;
    locationId?: string;
    startDate?: Date;
    endDate?: Date;
    targetDate?: string; // Date we expect to see (e.g., "2025-06-02")
    limit?: number;
    callDirection?: 'inbound' | 'outbound' | 'both';
    officeHoursOnly?: boolean;
  }): Promise<{
    totalCallsInRange: number;
    callsByDate: Record<string, number>;
    sampleCalls: Array<{ id: string; date: string; phoneNumber: string }>;
    targetDateCalls: Array<{ id: string; date: string; phoneNumber: string }>;
    paginationTest: {
      firstPageCalls: Array<{ id: string; date: string; phoneNumber: string }>;
      secondPageCalls: Array<{ id: string; date: string; phoneNumber: string }>;
      lastDocIdFromFirstPage?: string;
    };
  }> {
    try {
      logger.info(
        { context: 'diagnoseDatePaginationIssue', params },
        'Starting date pagination diagnostic',
      );

      const {
        clinicId,
        locationId,
        startDate,
        endDate,
        targetDate = '2025-06-02',
        limit = 10,
        callDirection = 'both',
        officeHoursOnly = false,
      } = params;

      // Test 1: Get total calls in range
      let queryRef: admin.firestore.Query = callsCollection.orderBy('date', 'desc');

      if (clinicId) {
        queryRef = queryRef.where('clinicId', '==', clinicId);
      }

      if (startDate) {
        queryRef = queryRef.where('date', '>=', admin.firestore.Timestamp.fromDate(startDate));
      }

      if (endDate) {
        queryRef = queryRef.where('date', '<=', admin.firestore.Timestamp.fromDate(endDate));
      }

      const allCallsSnapshot = await queryRef.limit(100).get();
      const allCalls = allCallsSnapshot.docs.map(convertToCall);

      // Group calls by date
      const callsByDate: Record<string, number> = {};
      const sampleCalls: Array<{ id: string; date: string; phoneNumber: string }> = [];
      const targetDateCalls: Array<{ id: string; date: string; phoneNumber: string }> = [];

      allCalls.forEach(call => {
        const dateStr = call.date.toISOString().split('T')[0];
        callsByDate[dateStr] = (callsByDate[dateStr] || 0) + 1;

        sampleCalls.push({
          id: call.id,
          date: dateStr,
          phoneNumber: call.phoneNumber || 'N/A',
        });

        if (dateStr === targetDate) {
          targetDateCalls.push({
            id: call.id,
            date: dateStr,
            phoneNumber: call.phoneNumber || 'N/A',
          });
        }
      });

      // Test 2: Simulate pagination with EXACT same parameters as UI
      const firstPageResult = await this.fetchAndFilterCalls({
        limit,
        clinicId,
        locationId,
        startDate,
        endDate,
        callDirection,
        officeHoursOnly,
      });

      let secondPageResult: { calls: Call[]; lastDocId?: string; isLastPage: boolean } = {
        calls: [],
        lastDocId: undefined,
        isLastPage: true,
      };
      if (firstPageResult.lastDocId && !firstPageResult.isLastPage) {
        secondPageResult = await this.fetchAndFilterCalls({
          limit,
          clinicId,
          locationId,
          startDate,
          endDate,
          callDirection,
          officeHoursOnly,
          startAfterId: firstPageResult.lastDocId,
        });
      }

      const result = {
        totalCallsInRange: allCalls.length,
        callsByDate,
        sampleCalls: sampleCalls.slice(0, 20),
        targetDateCalls,
        paginationTest: {
          firstPageCalls: firstPageResult.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString().split('T')[0],
            phoneNumber: call.phoneNumber || 'N/A',
          })),
          secondPageCalls: secondPageResult.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString().split('T')[0],
            phoneNumber: call.phoneNumber || 'N/A',
          })),
          lastDocIdFromFirstPage: firstPageResult.lastDocId,
        },
      };

      logger.info(
        { context: 'diagnoseDatePaginationIssue', result },
        'Date pagination diagnostic complete',
      );
      return result;
    } catch (error) {
      logger.error(
        {
          context: 'diagnoseDatePaginationIssue',
          error: error instanceof Error ? error.message : String(error),
        },
        'Date pagination diagnostic failed',
      );
      throw error;
    }
  },

  async getCallsBySessionId(sessionId: string): Promise<Call[]> {
    try {
      const snapshot = await callsCollection.where('sessionId', '==', sessionId).get();
      return snapshot.docs.map(convertToCall);
    } catch (error) {
      console.error(`Error getting calls by sessionId ${sessionId}:`, error);
      throw error;
    }
  },

  async getCallsWithClientData(): Promise<(Call & { clientName: string })[]> {
    try {
      console.log('Getting calls with client data...');
      const calls = await this.getAllCalls();
      console.log(`Found ${calls.length} calls`);

      // Separate calls into those with and without clientName for efficient processing
      const callsWithExistingNames: (Call & { clientName: string })[] = [];
      const callsNeedingLookup: Call[] = [];

      calls.forEach(call => {
        // Fast path: Use existing clientName if it's available and not the default fallback
        if (call.clientName && call.clientName.trim() !== '' && call.clientName !== 'Patient') {
          callsWithExistingNames.push({ ...call, clientName: call.clientName });
        } else {
          callsNeedingLookup.push(call);
        }
      });

      console.log(
        `Fast path: ${callsWithExistingNames.length} calls with existing clientName, ` +
          `Slow path: ${callsNeedingLookup.length} calls need client lookup`,
      );

      // Slow path: Lookup client names for calls that need it (legacy data or missing clientName)
      const callsWithLookedUpNames = await Promise.all(
        callsNeedingLookup.map(async call => {
          console.log(`Processing call ${call.id} with clientId ${call.clientId}`);

          // Skip client lookup if clientId is invalid
          if (
            !call.clientId ||
            typeof call.clientId !== 'string' ||
            call.clientId.trim() === '' ||
            call.clientId === 'unknown'
          ) {
            console.warn(`Call ${call.id} has invalid clientId: ${call.clientId}`);
            return {
              ...call,
              clientName: 'Patient',
            };
          }

          try {
            const client = await clientsService.getClientById(call.clientId);
            const resolvedClientName = client?.fullName || 'Patient';
            console.log(
              `Client lookup for call ${call.id}: ${!!client}, name: ${resolvedClientName}`,
            );

            return {
              ...call,
              clientName: resolvedClientName,
            };
          } catch (error) {
            console.error(`Error fetching client for call ${call.id}:`, error);
            return {
              ...call,
              clientName: 'Patient',
            };
          }
        }),
      );

      // Combine both sets of results
      const allCallsWithClientData = [...callsWithExistingNames, ...callsWithLookedUpNames];

      console.log(
        `Completed: ${allCallsWithClientData.length} total calls processed ` +
          `(${callsWithExistingNames.length} fast, ${callsWithLookedUpNames.length} slow)`,
      );

      return allCallsWithClientData;
    } catch (error) {
      console.error('Error getting calls with client data:', error);
      throw error;
    }
  },

  async getCallsByClientId(clientId: string, clinicId?: number | null): Promise<Call[]> {
    try {
      // Create a simpler query without inequality filters
      let query = callsCollection.where('clientId', '==', clientId);

      // Add clinic filter if provided
      if (clinicId) {
        query = callsCollection.where('clientId', '==', clientId).where('clinicId', '==', clinicId);
      }

      const snapshot = await query.get();
      const allCalls = snapshot.docs.map(convertToCall);

      // Filter out calls with "Unknown" phoneNumber in memory
      return allCalls.filter(call => call.phoneNumber !== 'Unknown');
    } catch (error) {
      console.error('Error getting calls by client ID:', error);
      throw error;
    }
  },

  async getCallById(id: string): Promise<Call | null> {
    try {
      await ensureDatabaseInitialized();
      logger.debug(
        { context: 'callsService.getCallById', callId: id },
        'Getting call by ID via MySQL',
      );
      const call = await repositoryManager.calls.findById(id);
      logger.debug(
        { context: 'callsService.getCallById', callId: id, found: !!call },
        'Call retrieval via MySQL completed',
      );
      return call;
    } catch (error) {
      logger.error(
        {
          context: 'callsService.getCallById',
          callId: id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get call via MySQL',
      );
      throw error;
    }
  },

  async getCallWithDetail(
    id: string,
  ): Promise<
    (Call & { summary?: string; transcription?: string; transcriptionWithAudio?: string }) | null
  > {
    try {
      // 1. Try the MySQL repository first (preferred data source going forward)
      await ensureDatabaseInitialized();
      const mysqlCall = await repositoryManager.calls.findById(id);

      if (mysqlCall) {
        // MySQL call already contains merged callDetails data (summary, transcription, etc.)
        // No need to fetch from Firestore callDetails collection
        return mysqlCall;
      }

      // 2. Fallback to Firestore (legacy records)
      const callDoc = await callsCollection.doc(id).get();
      if (!callDoc.exists) return null;

      const call = convertToCall(callDoc);

      // For legacy Firestore calls, try to get the call detail if it exists
      // This is only for calls that haven't been migrated to MySQL yet
      const callDetail = await callDetailsService.getCallDetailByCallId(id);

      if (callDetail) {
        return {
          ...call,
          summary: callDetail.summary,
          voicemailSummary: callDetail.voicemailSummary,
          transcription: callDetail.transcription,
          transcriptionWithAudio: callDetail.transcriptionWithAudio,
        };
      }

      return call;
    } catch (error) {
      console.error('Error getting call with detail:', error);
      throw error;
    }
  },

  async createCall(call: Omit<Call, 'id'>, callId?: string): Promise<Call> {
    try {
      await ensureDatabaseInitialized();
      logger.info({ context: 'callsService.createCall' }, 'Creating call via MySQL');
      const id = callId || uuidv4();
      const callData: Call = {
        ...call,
        id,
        date: call.date || new Date(),
        duration: call.duration || '',
        type: call.type || CallType.OTHER,
        priorityScore: call.priorityScore || 0,
        urgent: call.urgent || false,
        tags: call.tags || [],
        hasVoiceMail: call.hasVoiceMail || false,
        isOutboundCall: call.isOutboundCall || false,
        callTypes: call.callTypes ?? [(call.type ?? CallType.OTHER) as number],
      };

      const createdCall = await repositoryManager.calls.create(callData, {
        generateEntityId: () => callId,
      });
      logger.info(
        { context: 'callsService.createCall', callId: createdCall.id },
        'Successfully created call via MySQL',
      );
      return createdCall;
    } catch (error) {
      logger.warn(
        {
          context: 'callsService.createCall',
          error: error instanceof Error ? error.message : String(error),
        },
        'MySQL call creation failed, falling back to Firestore',
      );

      // Fallback to Firestore creation
      try {
        const id = callId || uuidv4();
        const callData: Call = {
          ...call,
          id,
          date: call.date || new Date(),
          duration: call.duration || '',
          type: call.type || CallType.OTHER,
          priorityScore: call.priorityScore || 0,
          urgent: call.urgent || false,
          tags: call.tags || [],
          hasVoiceMail: call.hasVoiceMail || false,
          isOutboundCall: call.isOutboundCall || false,
          callTypes: call.callTypes ?? [(call.type ?? CallType.OTHER) as number],
        };

        // Create call directly in Firestore
        await callsCollection.doc(id).set({
          ...callData,
          date: admin.firestore.Timestamp.fromDate(callData.date),
          createdAt: admin.firestore.Timestamp.now(),
          updatedAt: admin.firestore.Timestamp.now(),
        });

        logger.info(
          { context: 'callsService.createCall', callId: id },
          'Successfully created call via Firestore fallback',
        );
        return callData;
      } catch (firestoreError) {
        logger.error(
          {
            context: 'callsService.createCall',
            error:
              firestoreError instanceof Error ? firestoreError.message : String(firestoreError),
          },
          'Both MySQL and Firestore call creation failed',
        );
        throw firestoreError;
      }
    }
  },

  async updateCall(id: string, call: Partial<Call>): Promise<void> {
    try {
      // Check if this is a MySQL call first - use forceMySQL to avoid fallback confusion
      await ensureDatabaseInitialized();
      const mysqlCall = await repositoryManager.calls.findById(id, { forceMySQL: true });

      if (mysqlCall) {
        // This is definitely a MySQL record, update it directly
        try {
          await repositoryManager.calls.update(id, call);
          logger.info(
            { context: 'callsService.updateCall', callId: id, call },
            'Successfully updated call via MySQL',
          );
          return;
        } catch (error) {
          // If dual-write fails because call doesn't exist in Firestore, just update MySQL
          if (
            error instanceof Error &&
            (error.message.includes('No document to update') ||
              error.message.includes('NOT_FOUND') ||
              error.message.includes('Failed to update in Firestore'))
          ) {
            logger.warn(
              { context: 'callsService.updateCall', callId: id, error: error.message },
              'Firestore update failed, falling back to MySQL-only update',
            );
            await repositoryManager.calls.update(id, call, { skipFirestore: true });
            logger.info(
              { context: 'callsService.updateCall', callId: id },
              'Successfully updated call via MySQL only (Firestore record not found)',
            );
            return;
          }
          // If MySQL update fails because record doesn't exist, fall back to Firestore
          if (
            error instanceof Error &&
            (error.message.includes('Entity not found after update') ||
              error.message.includes('Failed to update entity in MySQL') ||
              error.message.includes('does not exist in MySQL'))
          ) {
            logger.warn(
              { context: 'callsService.updateCall', callId: id, error: error.message },
              'MySQL update failed, falling back to Firestore-only update (record may not exist in MySQL)',
            );
            // The record exists in Firestore but not MySQL, update Firestore only
            // Don't use the repository manager, go directly to Firestore
            await callsService.updateCallInFirestoreOnly(id, call);
            logger.info(
              { context: 'callsService.updateCall', callId: id },
              'Successfully updated call via Firestore only (MySQL record not found)',
            );
            return;
          }
          throw error;
        }
      }

      // Fallback to Firestore update for legacy calls
      const { summary, transcription, transcriptionWithAudio, ...callUpdatesRaw } = call;

      // Handle any potential undefined values in the call updates
      const callUpdates = Object.entries(callUpdatesRaw).reduce(
        (acc, [key, value]) => {
          // Only include defined values in the update
          if (value !== undefined) {
            acc[key] = value;
          }
          return acc;
        },
        {} as Record<
          string,
          | admin.firestore.FieldValue
          | string
          | number
          | boolean
          | null
          | admin.firestore.Timestamp
          | Date
          | Array<string | number | boolean | null | object>
          | object
        >,
      );

      // Start a transaction to ensure consistency for Firestore calls
      await db.runTransaction(async transaction => {
        // Update the main call record if there are updates
        if (Object.keys(callUpdates).length > 0) {
          transaction.update(callsCollection.doc(id), callUpdates);
        }

        // If summary, transcription, or transcriptionWithAudio are provided, update or create CallDetail
        if (
          summary !== undefined ||
          transcription !== undefined ||
          transcriptionWithAudio !== undefined
        ) {
          // Check if CallDetail exists
          const callDetailSnapshot = await callDetailsCollection
            .where('callId', '==', id)
            .limit(1)
            .get();

          if (!callDetailSnapshot.empty) {
            // Update existing CallDetail
            const callDetailDoc = callDetailSnapshot.docs[0];
            const updateData: Record<string, admin.firestore.FieldValue | string | Date> = {
              updatedAt: new Date(),
            };

            if (summary !== undefined) updateData.summary = summary || '';
            if (transcription !== undefined) updateData.transcription = transcription || '';
            if (transcriptionWithAudio !== undefined)
              updateData.transcriptionWithAudio = transcriptionWithAudio || '';

            transaction.update(callDetailsCollection.doc(callDetailDoc.id), updateData);
          } else {
            // Create new CallDetail
            const callDetailId = uuidv4();
            const now = new Date();
            transaction.set(callDetailsCollection.doc(callDetailId), {
              id: callDetailId,
              callId: id,
              summary: summary || '',
              transcription: transcription || '',
              transcriptionWithAudio: transcriptionWithAudio || '',
              createdAt: now,
              updatedAt: now,
            });
          }
        }
      });
    } catch (error) {
      console.error('Error updating call:', error);
      throw error;
    }
  },

  async updateCallInFirestoreOnly(id: string, call: Partial<Call>): Promise<void> {
    try {
      // Update call directly in Firestore without dual-write
      const { summary, transcription, transcriptionWithAudio, ...callUpdatesRaw } = call;

      // Handle any potential undefined values in the call updates
      const callUpdates = Object.entries(callUpdatesRaw).reduce(
        (acc, [key, value]) => {
          // Only include defined values in the update
          if (value !== undefined) {
            acc[key] = value;
          }
          return acc;
        },
        {} as Record<
          string,
          | admin.firestore.FieldValue
          | string
          | number
          | boolean
          | null
          | admin.firestore.Timestamp
          | Date
          | Array<string | number | boolean | null | object>
          | object
        >,
      );

      // Start a transaction to ensure consistency for Firestore calls
      await db.runTransaction(async transaction => {
        // Update the main call record if there are updates
        if (Object.keys(callUpdates).length > 0) {
          transaction.update(callsCollection.doc(id), callUpdates);
        }

        // If summary, transcription, or transcriptionWithAudio are provided, update or create CallDetail
        if (
          summary !== undefined ||
          transcription !== undefined ||
          transcriptionWithAudio !== undefined
        ) {
          // Check if CallDetail exists
          const callDetailSnapshot = await callDetailsCollection
            .where('callId', '==', id)
            .limit(1)
            .get();

          if (!callDetailSnapshot.empty) {
            // Update existing CallDetail
            const callDetailDoc = callDetailSnapshot.docs[0];
            const updateData: Record<string, admin.firestore.FieldValue | string | Date> = {
              updatedAt: new Date(),
            };

            if (summary !== undefined) updateData.summary = summary || '';
            if (transcription !== undefined) updateData.transcription = transcription || '';
            if (transcriptionWithAudio !== undefined)
              updateData.transcriptionWithAudio = transcriptionWithAudio || '';

            transaction.update(callDetailsCollection.doc(callDetailDoc.id), updateData);
          } else {
            // Create new CallDetail
            const callDetailId = uuidv4();
            const now = new Date();
            transaction.set(callDetailsCollection.doc(callDetailId), {
              id: callDetailId,
              callId: id,
              summary: summary || '',
              transcription: transcription || '',
              transcriptionWithAudio: transcriptionWithAudio || '',
              createdAt: now,
              updatedAt: now,
            });
          }
        }
      });
    } catch (error) {
      console.error('Error updating call in Firestore only:', error);
      throw error;
    }
  },

  async deleteCall(id: string): Promise<void> {
    try {
      // Start a transaction to ensure consistency
      await db.runTransaction(async transaction => {
        // Delete the main call record
        transaction.delete(callsCollection.doc(id));

        // Find and delete any associated CallDetail
        const callDetailSnapshot = await callDetailsCollection
          .where('callId', '==', id)
          .limit(1)
          .get();
        if (!callDetailSnapshot.empty) {
          transaction.delete(callDetailsCollection.doc(callDetailSnapshot.docs[0].id));
        }
      });
    } catch (error) {
      console.error('Error deleting call:', error);
      throw error;
    }
  },

  async getCallsCount(): Promise<number> {
    try {
      const snapshot = await callsCollection.get();
      return snapshot.size;
    } catch (error) {
      console.error('Error getting calls count:', error);
      throw error;
    }
  },

  async paginateCalls(params: {
    limit: number;
    startAfterId?: string;
    clinicId?: number | null;
    locationId?: string;
    startDate?: Date;
    endDate?: Date;
    searchTerm?: string;
    callType?: CallType;
    minPriority?: number;
    maxPriority?: number;
    officeHoursOnly?: boolean;
    callDirection?: 'inbound' | 'outbound' | 'both';
    includeUnknownPhoneNumbers?: boolean;
  }): Promise<{
    calls: Call[];
    lastDocId?: string;
    isLastPage: boolean;
  }> {
    const {
      limit = 10,
      startAfterId,
      clinicId,
      locationId,
      startDate,
      endDate,
      searchTerm,
      callType,
      minPriority,
      maxPriority,
      callDirection,
      includeUnknownPhoneNumbers,
      officeHoursOnly,
    } = params;

    logger.info(
      { context: 'callsService.paginateCalls', params },
      'Getting paginated calls via MySQL',
    );

    try {
      // Ensure database is initialized before using repositories
      await ensureDatabaseInitialized();

      // Use offset-based pagination to maintain consistent date ordering
      // Calculate offset based on startAfterId (for now, we'll use a simple offset calculation)
      let offset = 0;
      if (startAfterId) {
        // For now, we'll parse the offset from the startAfterId
        // In a real implementation, you might want to store the offset in the cursor
        // or calculate it based on the position of the record
        const offsetMatch = startAfterId.match(/offset_(\d+)/);
        if (offsetMatch) {
          offset = parseInt(offsetMatch[1], 10);
        }
      }

      // Safely convert locationId to number, avoiding NaN
      let locationIdNumber: number | undefined = undefined;
      if (locationId) {
        const parsed = Number(locationId);
        if (!isNaN(parsed) && isFinite(parsed)) {
          locationIdNumber = parsed;
        }
      }

      // Validate all parameters before passing to repository
      const repositoryParams = {
        limit: Math.max(1, Math.min(limit, 100)), // Ensure valid limit range
        offset, // Use offset-based pagination
        clinicId: clinicId || undefined,
        locationId: locationIdNumber,
        startDate,
        endDate,
        searchTerm: searchTerm?.trim() || undefined, // Trim search term
        callType,
        minPriority: minPriority !== undefined ? Math.max(0, minPriority) : undefined,
        maxPriority: maxPriority !== undefined ? Math.max(0, maxPriority) : undefined,
        callDirection: callDirection || 'both',
        includeUnknownPhoneNumbers: includeUnknownPhoneNumbers ?? false,
        officeHoursOnly: officeHoursOnly ?? false, // Pass office hours filtering to repository
        // Don't use afterId for cursor-based pagination to avoid ordering issues
      };

      // Log parameters for debugging
      logger.debug(
        {
          context: 'callsService.paginateCalls',
          repositoryParams,
        },
        'Calling repository with validated parameters',
      );

      const result = await repositoryManager.calls.fetchAndFilterCalls(repositoryParams);

      logger.info(
        {
          context: 'callsService.paginateCalls',
          callsReturned: result.calls.length,
          hasMore: result.hasMore,
          offset,
          nextOffset: offset + result.calls.length,
        },
        'MySQL pagination completed successfully',
      );

      // Process calls for automatic DISCONNECTED marking
      await this.processCallsForAutoDisconnected(result.calls, 'pagination-auto-disconnected');

      // Create a new cursor that includes the offset for next page
      const nextOffset = offset + result.calls.length;
      const nextCursor = result.hasMore ? `offset_${nextOffset}` : undefined;

      return {
        calls: result.calls,
        lastDocId: nextCursor,
        isLastPage: !result.hasMore,
      };
    } catch (err) {
      logger.error(
        {
          context: 'callsService.paginateCalls',
          error: err instanceof Error ? err.message : String(err),
          stack: err instanceof Error ? err.stack : undefined,
        },
        'Failed to paginate calls via MySQL - this should not happen in production',
      );

      // In production, we should not fall back to Firestore
      // Instead, we should fix the MySQL connection/configuration
      throw new Error(
        `MySQL pagination failed: ${err instanceof Error ? err.message : String(err)}`,
      );
    }
  },

  // Helper function to fetch and filter calls recursively until we have enough items
  async fetchAndFilterCalls(params: {
    limit: number;
    startAfterId?: string;
    clinicId?: number | null;
    locationId?: string;
    startDate?: Date;
    endDate?: Date;
    searchTerm?: string;
    callType?: CallType;
    minPriority?: number;
    maxPriority?: number;
    officeHoursOnly?: boolean;
    accumulatedCalls?: Call[];
    fetchAttempt?: number;
    lastFetchedDocId?: string; // Track the last document from actual Firestore query
    callDirection?: 'inbound' | 'outbound' | 'both';
    includeUnknownPhoneNumbers?: boolean; // New parameter
  }): Promise<{
    calls: Call[];
    lastDocId?: string;
    isLastPage: boolean;
  }> {
    const {
      limit,
      startAfterId,
      clinicId,
      locationId,
      startDate,
      endDate,
      searchTerm,
      callType,
      minPriority,
      maxPriority,
      officeHoursOnly,
      accumulatedCalls = [],
      fetchAttempt = 1,
      lastFetchedDocId,
      callDirection,
    } = params;

    logger.info(
      {
        context: 'fetchAndFilterCalls',
        fetchAttempt,
        startAfterId,
        lastFetchedDocId,
        accumulatedCallsCount: accumulatedCalls.length,
        limit,
      },
      'Starting fetch attempt',
    );

    // Pre-fetch location data when office hours filtering is needed
    let locationData: Location | null = null;
    let locationsByClinic: Record<string, Location> = {};

    if (officeHoursOnly) {
      if (locationId && clinicId) {
        // Fetch specific location (requires both locationId and clinicId)
        locationData = await LocationService.getLocationById(locationId, clinicId);
        if (!locationData) {
          logger.warn(
            { context: 'fetchAndFilterCalls', locationId, clinicId },
            'Location not found for office hours filtering',
          );
        } else {
          logger.info(
            {
              context: 'fetchAndFilterCalls',
              locationId,
              clinicId,
              locationName: locationData.name,
              timeZone: locationData.timeZone,
              officeHours: locationData.officeHours,
            },
            'Loaded location data for office hours filtering',
          );
        }
      } else if (clinicId) {
        // Fetch all locations for the clinic
        const clinicLocationResult = await LocationService.getLocationsByClinicId(clinicId);
        locationsByClinic = clinicLocationResult.locations.reduce(
          (acc, loc) => {
            acc[String(loc.id)] = loc;
            return acc;
          },
          {} as Record<string, Location>,
        );
        logger.info(
          {
            context: 'fetchAndFilterCalls',
            clinicId,
            locationCount: clinicLocationResult.locations.length,
          },
          'Loaded locations for clinic office hours filtering',
        );
      }
    }

    // Helper function to check if a call is within office hours for a given location
    const isWithinOfficeHours = (call: Call, location: Location): boolean => {
      try {
        // Use the proper OfficeHoursService for timezone conversion and office hours checking
        const officeHours = location.officeHours || {};
        const timeZone = location.timeZone || 'America/Chicago'; // Default to Central Time

        // If no office hours configured, include the call (can't filter what we don't know)
        if (!location.officeHours || Object.keys(location.officeHours).length === 0) {
          logger.debug(
            {
              context: 'isWithinOfficeHours',
              locationId: location.id,
              hasOfficeHours: !!location.officeHours,
              hasTimeZone: !!location.timeZone,
            },
            'Location missing office hours configuration - including call in results',
          );

          return true; // Include call if no office hours configured
        }

        // Use the OfficeHoursService for proper timezone conversion
        const status = OfficeHoursService.checkOfficeHours(officeHours, timeZone, call.date);

        logger.debug(
          {
            context: 'isWithinOfficeHours',
            callId: call.id,
            locationId: location.id,
            callDate: call.date.toISOString(),
            timeZone,
            isOpen: status.isOpen,
            currentStatus: status.currentStatus,
          },
          'Office hours check completed',
        );

        return status.isOpen;
      } catch (error) {
        logger.error(
          {
            context: 'isWithinOfficeHours',
            callId: call.id,
            locationId: location.id,
            error: error instanceof Error ? error.message : String(error),
          },
          'Error checking office hours, defaulting to false',
        );
        return false;
      }
    };

    // Check if we can do phone search at database level (exact match only)
    const normalizedSearchTerm = searchTerm ? normalizePhoneNumber(searchTerm) : '';
    const canUseDbPhoneSearch =
      normalizedSearchTerm.length >= 10 && !minPriority && !maxPriority && !officeHoursOnly;

    // Determine if we need post-filtering (which requires larger batch sizes)
    const needsPostFiltering =
      (!canUseDbPhoneSearch && searchTerm) ||
      officeHoursOnly ||
      locationId ||
      minPriority !== undefined ||
      maxPriority !== undefined;

    // Cap the fetch size based on filtering needs
    const remainingNeeded = limit - accumulatedCalls.length;
    const batchSize = needsPostFiltering
      ? Math.min(Math.max(remainingNeeded * 5, 25), 150) // More aggressive for post-filtering
      : Math.min(Math.max(remainingNeeded * 2, 15), 75); // Conservative for DB filtering

    // Build query with ALL database-compatible filters
    let queryRef: admin.firestore.Query = callsCollection;

    // Detect if we need composite indexes (which we want to avoid)
    const hasFiltersRequiringCompositeIndex =
      clinicId !== undefined ||
      callDirection !== 'both' ||
      callType !== undefined ||
      canUseDbPhoneSearch;

    // Only use orderBy if we don't have filters that would require composite indexes
    const useOrderBy = !hasFiltersRequiringCompositeIndex;

    if (useOrderBy) {
      queryRef = queryRef.orderBy('date', 'desc');
    }

    // Apply call direction filter
    if (callDirection !== 'both') {
      if (callDirection === 'outbound') {
        queryRef = queryRef.where('isOutboundCall', '==', true);
      } else {
        queryRef = queryRef.where('isOutboundCall', '!=', true);
      }
    }

    // Apply clinic filter if provided
    if (clinicId) {
      queryRef = queryRef.where('clinicId', '==', clinicId);
    }

    // Note: Location filtering is handled in post-filtering due to type mismatch issues
    // (locationId can be string or number, and Firestore queries are type-strict)

    // Apply phone search at database level if possible (exact match)
    if (canUseDbPhoneSearch) {
      // Try different phone number variations for exact matches
      const phoneTokens = generatePhoneSearchTokens(normalizedSearchTerm);
      // For now, use the most complete phone number
      const primaryPhone = phoneTokens.find(token => token.length >= 10) || phoneTokens[0];
      queryRef = queryRef.where('phoneNumber', '==', primaryPhone);
    }

    // Apply call type filter at database level (create indexes as needed)
    if (callType !== undefined) {
      queryRef = queryRef.where('type', '==', callType);
    }

    // Note: Priority filtering is handled in post-filtering due to Firestore inequality constraints
    // (Firestore only allows one inequality filter per query, and we're already ordering by 'date')

    // Apply date filters if provided
    if (startDate) {
      queryRef = queryRef.where('date', '>=', admin.firestore.Timestamp.fromDate(startDate));
    }

    if (endDate) {
      queryRef = queryRef.where('date', '<=', admin.firestore.Timestamp.fromDate(endDate));
    }

    // Determine which document to start after for pagination
    let startAfterDoc: admin.firestore.DocumentSnapshot | undefined = undefined;
    const docIdToStartAfter = lastFetchedDocId || startAfterId;

    if (docIdToStartAfter) {
      const startAfterDocRef = await callsCollection.doc(docIdToStartAfter).get();
      if (startAfterDocRef.exists) {
        startAfterDoc = startAfterDocRef;
        logger.info(
          {
            context: 'fetchAndFilterCalls',
            docIdToStartAfter,
            startAfterDocDate: startAfterDocRef.data()?.date?.toDate?.()?.toISOString(),
          },
          'Using pagination cursor',
        );
      } else {
        logger.warn(
          { context: 'fetchAndFilterCalls', docIdToStartAfter, fetchAttempt },
          'Start after document does not exist',
        );
      }
    }

    // Fetch the next batch
    const { items, isLastPage, lastDoc } = await paginateQuery({
      queryRef,
      mapper: convertToCall,
      limit: batchSize,
      startAfterDoc,
    });

    logger.info(
      {
        context: 'fetchAndFilterCalls',
        itemsFetched: items.length,
        isLastPage,
        lastDocId: lastDoc?.id,
        lastDocDate: lastDoc?.data()?.date?.toDate?.()?.toISOString(),
        sampleItemDates: items
          .slice(0, 3)
          .map(item => ({ id: item.id, date: item.date.toISOString() })),
      },
      'Fetched batch from Firestore',
    );

    // Track the last document ID from this fetch for pagination
    const currentLastFetchedDocId = lastDoc ? lastDoc.id : lastFetchedDocId;

    // Temporary blacklist of phone numbers to filter out
    const phoneNumberBlacklist = [
      '9178582585',
      '9082740595',
      '9176268074',
      '7328229112',
      '15169298899',
      '12252542523',
    ];

    // Get the GCP_AGENT_ID from environment variable for agent filtering
    const gcp_agent_id = process.env.GCP_AGENT_ID;

    // Apply ONLY filters that cannot be done at database level
    const filteredBatch = items.filter(call => {
      // Filter by agentId: only show calls with no agentId (legacy calls) or calls with matching agentId
      if (gcp_agent_id) {
        const callAgentId = call.agentId;
        const hasNoAgentId = !callAgentId || callAgentId.trim() === '';
        const matchesCurrentAgent = callAgentId === gcp_agent_id;

        if (!hasNoAgentId && !matchesCurrentAgent) {
          logger.debug(
            {
              context: 'fetchAndFilterCalls',
              callId: call.id,
              callAgentId,
              gcp_agent_id,
              date: call.date.toISOString(),
            },
            'Filtered out call due to agentId mismatch',
          );
          return false;
        }
      }

      // Filter out calls with invalid phone numbers (both empty and "Unknown")
      if (!call.phoneNumber || call.phoneNumber.trim() === '' || call.phoneNumber === 'Unknown') {
        logger.debug(
          {
            context: 'fetchAndFilterCalls',
            callId: call.id,
            phoneNumber: call.phoneNumber,
            date: call.date.toISOString(),
          },
          'Filtered out call due to invalid phone number',
        );
        return false;
      }

      // Temporary filter: exclude blacklisted phone numbers
      const normalizedCallPhone = normalizePhoneNumber(call.phoneNumber);
      if (
        phoneNumberBlacklist.some(
          blacklistedPhone =>
            normalizedCallPhone.includes(blacklistedPhone) ||
            blacklistedPhone.includes(normalizedCallPhone),
        )
      ) {
        logger.debug(
          {
            context: 'fetchAndFilterCalls',
            callId: call.id,
            phoneNumber: call.phoneNumber,
            normalizedPhone: normalizedCallPhone,
            date: call.date.toISOString(),
          },
          'Filtered out call due to blacklisted phone number',
        );
        return false;
      }

      // Additional location filtering to handle type mismatches (string vs number)
      if (locationId) {
        const callLocationId = call.locationId;
        // Convert both to strings for comparison to handle mixed types
        const callLocationIdStr = String(callLocationId);
        const locationIdStr = String(locationId);

        if (callLocationIdStr !== locationIdStr) {
          logger.debug(
            {
              context: 'fetchAndFilterCalls',
              callId: call.id,
              callLocationId,
              locationId,
              date: call.date.toISOString(),
            },
            'Filtered out call due to location mismatch',
          );
          return false;
        }
      }

      // Apply search filter (phone number) - only if not handled at DB level
      if (!canUseDbPhoneSearch && searchTerm && searchTerm.trim() !== '') {
        const normalizedPhoneNumber = normalizePhoneNumber(call.phoneNumber || '');
        const searchTokens = generatePhoneSearchTokens(normalizedSearchTerm);

        // Check if any search token matches any part of the phone number
        const hasMatch = searchTokens.some(
          token => normalizedPhoneNumber.includes(token) || token.includes(normalizedPhoneNumber),
        );

        if (!hasMatch) {
          logger.debug(
            {
              context: 'fetchAndFilterCalls',
              callId: call.id,
              phoneNumber: call.phoneNumber,
              searchTerm,
              date: call.date.toISOString(),
            },
            'Filtered out call due to search term mismatch',
          );
          return false;
        }
      }

      // Apply priority filtering
      if (minPriority !== undefined && (call.priorityScore || 0) < minPriority) {
        logger.debug(
          {
            context: 'fetchAndFilterCalls',
            callId: call.id,
            priorityScore: call.priorityScore,
            minPriority,
            date: call.date.toISOString(),
          },
          'Filtered out call due to low priority',
        );
        return false;
      }

      if (maxPriority !== undefined && (call.priorityScore || 0) > maxPriority) {
        logger.debug(
          {
            context: 'fetchAndFilterCalls',
            callId: call.id,
            priorityScore: call.priorityScore,
            maxPriority,
            date: call.date.toISOString(),
          },
          'Filtered out call due to high priority',
        );
        return false;
      }

      // Apply office hours filter using location data from database
      if (officeHoursOnly) {
        let callLocation: Location | null = null;

        if (locationData) {
          // Use pre-fetched specific location
          callLocation = locationData;
        } else if (call.locationId && locationsByClinic[String(call.locationId)]) {
          // Use location from clinic locations lookup
          callLocation = locationsByClinic[String(call.locationId)];
        } else if (call.locationId) {
          // Fallback: fetch location on demand (less efficient but covers edge cases)
          logger.warn(
            {
              context: 'fetchAndFilterCalls',
              callId: call.id,
              callLocationId: call.locationId,
              fetchAttempt,
            },
            'Location not pre-loaded, using fallback business hours',
          );
        }

        if (callLocation) {
          const withinHours = isWithinOfficeHours(call, callLocation);
          if (!withinHours) {
            logger.debug(
              {
                context: 'fetchAndFilterCalls',
                callId: call.id,
                locationId: callLocation.id,
                date: call.date.toISOString(),
              },
              'Filtered out call due to office hours (location-based)',
            );
            return false;
          }
        } else {
          // No location data available - cannot filter by office hours
          logger.debug(
            {
              context: 'fetchAndFilterCalls',
              callId: call.id,
              date: call.date.toISOString(),
            },
            'No location data available - including call in results',
          );
          // Include call if we can't determine location office hours
          return true;
        }
      }

      return true;
    });

    logger.info(
      {
        context: 'fetchAndFilterCalls',
        itemsBeforeFilter: items.length,
        itemsAfterFilter: filteredBatch.length,
        filteredItemDates: filteredBatch
          .slice(0, 3)
          .map(item => ({ id: item.id, date: item.date.toISOString() })),
      },
      'Applied post-filtering',
    );

    // Add to our accumulated result
    const newAccumulatedCalls = [...accumulatedCalls, ...filteredBatch];

    logger.info(
      {
        context: 'fetchAndFilterCalls',
        accumulatedTotal: newAccumulatedCalls.length,
        targetLimit: limit,
        isLastPage,
        fetchAttempt,
      },
      'Current accumulation status',
    );

    // If we have enough items or there are no more items to fetch, return the result
    if (
      newAccumulatedCalls.length >= limit ||
      isLastPage ||
      items.length === 0 ||
      fetchAttempt >= 8 // Increased attempts since we're doing more DB-level filtering
    ) {
      // CRITICAL FIX: Use the last RETURNED call ID for pagination, not the last fetched document
      // This ensures we don't skip valid calls that exist between the returned calls and fetched calls
      const lastReturnedCall =
        newAccumulatedCalls.length > 0
          ? newAccumulatedCalls[Math.min(newAccumulatedCalls.length - 1, limit - 1)]
          : null;
      const finalLastDocId = lastReturnedCall ? lastReturnedCall.id : currentLastFetchedDocId;

      // We've reached the last page if:
      // 1. Firestore reports no more data (isLastPage = true), OR
      // 2. We got an empty batch (items.length === 0), OR
      // 3. We hit max attempts and have fewer results than requested
      const actualIsLastPage =
        isLastPage ||
        items.length === 0 ||
        (fetchAttempt >= 8 && newAccumulatedCalls.length < limit);

      logger.info(
        {
          context: 'fetchAndFilterCalls',
          finalCallsReturned: Math.min(newAccumulatedCalls.length, limit),
          finalLastDocId,
          actualIsLastPage,
          finalCallDates: newAccumulatedCalls.slice(0, Math.min(limit, 5)).map(call => ({
            id: call.id,
            date: call.date.toISOString(),
          })),
        },
        'Returning final result',
      );

      const finalCalls = newAccumulatedCalls.slice(0, limit);

      // Process calls for automatic DISCONNECTED marking
      await this.processCallsForAutoDisconnected(finalCalls, 'firestore-auto-disconnected');

      return {
        calls: finalCalls, // Ensure we don't return more than requested
        lastDocId: finalLastDocId,
        isLastPage: actualIsLastPage,
      };
    }

    // If we don't have enough items yet, fetch more
    // Use the last fetched document ID for pagination, not the last accumulated call
    logger.info(
      {
        context: 'fetchAndFilterCalls',
        currentAccumulated: newAccumulatedCalls.length,
        needed: limit,
        willContinueWith: currentLastFetchedDocId,
      },
      'Continuing to next fetch attempt',
    );

    return this.fetchAndFilterCalls({
      limit,
      clinicId,
      locationId,
      startDate,
      endDate,
      searchTerm,
      callType,
      minPriority,
      maxPriority,
      officeHoursOnly,
      callDirection,
      accumulatedCalls: newAccumulatedCalls,
      fetchAttempt: fetchAttempt + 1,
      lastFetchedDocId: currentLastFetchedDocId, // This ensures pagination advances even if all items were filtered
    });
  },

  /**
   * Process calls for automatic DISCONNECTED marking
   * Marks calls over 20 seconds with no meaningful call type as DISCONNECTED
   * @param calls Array of calls to process
   * @param context Additional context for logging
   */
  async processCallsForAutoDisconnected(
    calls: Call[],
    context: string = 'auto-disconnected-batch',
  ): Promise<void> {
    const { processCallForAutoDisconnected } = await import(
      '../lib/external-api/v2/utils/call-type-utils'
    );

    for (const call of calls) {
      try {
        const updatedType = await processCallForAutoDisconnected(call, context);
        if (updatedType) {
          // Update the call object in place for immediate reflection
          call.type = updatedType;
          // Replace the callTypes array with the new type only (don't append)
          call.callTypes = [updatedType];
        }
      } catch (error) {
        logger.error(
          { callId: call.id, error, context },
          'Failed to process call for auto-disconnected marking',
        );
        // Continue processing other calls even if one fails
      }
    }
  },
};

export const afterHoursCallsService = {
  async getAfterHoursCalls(): Promise<AfterHoursCall[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsService.getAfterHoursCalls' },
        'Getting after-hours calls via MySQL',
      );
      const result = await repositoryManager.afterHoursCalls.findMany({});
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsService.getAfterHoursCalls',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours calls via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallById(id: string): Promise<AfterHoursCall | null> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsService.getAfterHoursCallById', id },
        'Getting after-hours call by ID via MySQL',
      );
      const result = await repositoryManager.afterHoursCalls.findById(id);
      return result;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsService.getAfterHoursCallById',
          id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call by ID via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallByCallId(callId: string): Promise<AfterHoursCall | null> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsService.getAfterHoursCallByCallId', callId },
        'Getting after-hours call by call ID via MySQL',
      );
      const result = await repositoryManager.afterHoursCalls.findByCallId(callId);
      return result.items.length > 0 ? result.items[0] : null;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsService.getAfterHoursCallByCallId',
          callId,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call by call ID via MySQL',
      );
      throw error;
    }
  },

  async createAfterHoursCall(afterHoursCall: Omit<AfterHoursCall, 'id'>): Promise<AfterHoursCall> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsService.createAfterHoursCall' },
        'Creating after-hours call via MySQL',
      );
      const result = await repositoryManager.afterHoursCalls.create(afterHoursCall);
      return result;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsService.createAfterHoursCall',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to create after-hours call via MySQL',
      );
      throw error;
    }
  },

  async updateAfterHoursCall(id: string, afterHoursCall: Partial<AfterHoursCall>): Promise<void> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsService.updateAfterHoursCall' },
        'Updating after-hours call via MySQL',
      );
      await repositoryManager.afterHoursCalls.update(id, afterHoursCall);
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsService.updateAfterHoursCall',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to update after-hours call via MySQL',
      );
      throw error;
    }
  },
};

// After Hours Calls Log Service
export const afterHoursCallsLogService = {
  async getAfterHoursCallsLogs(): Promise<AfterHoursCallLog[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getAfterHoursCallsLogs' },
        'Getting after-hours calls logs via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.findMany({});
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallsLogs',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours calls logs via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallLogById(id: string): Promise<AfterHoursCallLog | null> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getAfterHoursCallLogById', id },
        'Getting after-hours call log by ID via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.findById(id);
      return result;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallLogById',
          id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call log by ID via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallLogsByAfterHoursCallId(
    afterHoursCallId: string,
  ): Promise<AfterHoursCallLog[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallLogsByAfterHoursCallId',
          afterHoursCallId,
        },
        'Getting after-hours call logs by after hours call ID via MySQL',
      );
      const result =
        await repositoryManager.afterHoursCallsLog.findByAfterHoursCallId(afterHoursCallId);
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallLogsByAfterHoursCallId',
          afterHoursCallId,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call logs by after hours call ID via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallLogsByViewedBy(viewedBy: string): Promise<AfterHoursCallLog[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getAfterHoursCallLogsByViewedBy', viewedBy },
        'Getting after-hours call logs by viewed by user via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.findByViewedBy(viewedBy);
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallLogsByViewedBy',
          viewedBy,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call logs by viewed by user via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallLogsByContactedBy(contactedBy: string): Promise<AfterHoursCallLog[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getAfterHoursCallLogsByContactedBy', contactedBy },
        'Getting after-hours call logs by contacted by user via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.findByContactedBy(contactedBy);
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallLogsByContactedBy',
          contactedBy,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call logs by contacted by user via MySQL',
      );
      throw error;
    }
  },

  async getViewedNotContactedLogs(): Promise<AfterHoursCallLog[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getViewedNotContactedLogs' },
        'Getting viewed but not contacted after-hours call logs via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.findViewedNotContacted();
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getViewedNotContactedLogs',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get viewed but not contacted after-hours call logs via MySQL',
      );
      throw error;
    }
  },

  async getContactedLogs(): Promise<AfterHoursCallLog[]> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getContactedLogs' },
        'Getting contacted after-hours call logs via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.findContacted();
      return result.items;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getContactedLogs',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get contacted after-hours call logs via MySQL',
      );
      throw error;
    }
  },

  async createAfterHoursCallLog(
    afterHoursCallLog: Omit<AfterHoursCallLog, 'id'>,
  ): Promise<AfterHoursCallLog> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.createAfterHoursCallLog' },
        'Creating after-hours call log via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.create(afterHoursCallLog);
      return result;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.createAfterHoursCallLog',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to create after-hours call log via MySQL',
      );
      throw error;
    }
  },

  async updateAfterHoursCallLog(
    id: string,
    afterHoursCallLog: Partial<AfterHoursCallLog>,
  ): Promise<void> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.updateAfterHoursCallLog' },
        'Updating after-hours call log via MySQL',
      );
      await repositoryManager.afterHoursCallsLog.update(id, afterHoursCallLog);
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.updateAfterHoursCallLog',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to update after-hours call log via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallLogStats(): Promise<{
    totalLogs: number;
    viewedLogs: number;
    contactedLogs: number;
    logsByUser: Record<string, { viewed: number; contacted: number }>;
  }> {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getAfterHoursCallLogStats' },
        'Getting after-hours call log statistics via MySQL',
      );
      const result = await repositoryManager.afterHoursCallsLog.getAfterHoursCallLogStats();
      return result;
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallLogStats',
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call log statistics via MySQL',
      );
      throw error;
    }
  },

  async getAfterHoursCallLogsByCallIdWithUserNames(callId: string): Promise<
    Array<{
      id: string;
      afterHoursCallId: string;
      viewedBy: string | undefined;
      contactedBy: string | undefined;
      contactedByInfo: string | undefined;
      createdAt: Date;
      updatedAt: Date;
      viewedByUserName: string | undefined;
      contactedByUserName: string | undefined;
    }>
  > {
    try {
      await ensureDatabaseInitialized();
      logger.info(
        { context: 'afterHoursCallsLogService.getAfterHoursCallLogsByCallIdWithUserNames', callId },
        'Getting after-hours call logs by call ID with user names via MySQL',
      );
      return await repositoryManager.afterHoursCallsLog.findByCallIdWithUserNames(callId);
    } catch (error) {
      logger.error(
        {
          context: 'afterHoursCallsLogService.getAfterHoursCallLogsByCallIdWithUserNames',
          callId,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to get after-hours call logs by call ID with user names via MySQL',
      );
      throw error;
    }
  },
};

// Firestore CRUD operations for Calendar Slots
export const calendarSlotsService = {
  async getAllSlots(): Promise<CalendarSlot[]> {
    try {
      const snapshot = await calendarSlotsCollection.get();
      return snapshot.docs.map(convertToCalendarSlot);
    } catch (error) {
      console.error('Error getting calendar slots:', error);
      throw error;
    }
  },

  async getSlotsByUserId(userId: string): Promise<CalendarSlot[]> {
    try {
      const snapshot = await calendarSlotsCollection.where('userId', '==', userId).get();
      return snapshot.docs.map(convertToCalendarSlot);
    } catch (error) {
      console.error('Error getting calendar slots by userId:', error);
      throw error;
    }
  },

  async getSlotsByDate(date: string): Promise<CalendarSlot[]> {
    try {
      const snapshot = await calendarSlotsCollection.where('date', '==', date).get();
      return snapshot.docs.map(convertToCalendarSlot);
    } catch (error) {
      console.error('Error getting calendar slots by date:', error);
      throw error;
    }
  },

  async getSlotByUserIdAndDate(userId: string, date: string): Promise<CalendarSlot | null> {
    try {
      const snapshot = await calendarSlotsCollection
        .where('userId', '==', userId)
        .where('date', '==', date)
        .limit(1)
        .get();

      if (snapshot.empty) return null;
      return convertToCalendarSlot(snapshot.docs[0]);
    } catch (error) {
      console.error('Error getting calendar slot by userId and date:', error);
      throw error;
    }
  },

  async createSlot(slot: Omit<CalendarSlot, 'id'>): Promise<CalendarSlot> {
    try {
      const id = uuidv4();
      const newSlot = { ...slot, id };
      await calendarSlotsCollection.doc(id).set(newSlot);
      return newSlot as CalendarSlot;
    } catch (error) {
      console.error('Error creating calendar slot:', error);
      throw error;
    }
  },

  async updateSlot(id: string, slot: Partial<CalendarSlot>): Promise<void> {
    try {
      await calendarSlotsCollection.doc(id).update(slot);
    } catch (error) {
      console.error('Error updating calendar slot:', error);
      throw error;
    }
  },

  async updateSlotAvailability(
    userId: string,
    date: string,
    slotId: string,
    isAvailable: boolean,
  ): Promise<void> {
    try {
      const snapshot = await calendarSlotsCollection
        .where('userId', '==', userId)
        .where('date', '==', date)
        .limit(1)
        .get();

      if (snapshot.empty) {
        throw new Error(`No calendar slots found for user ${userId} on date ${date}`);
      }

      const doc = snapshot.docs[0];
      const data = doc.data();
      const timeSlots = data.timeSlots || [];

      const updatedTimeSlots = timeSlots.map((slot: TimeSlot) => {
        if (slot.id === slotId) {
          return { ...slot, isAvailable };
        }
        return slot;
      });

      await calendarSlotsCollection.doc(doc.id).update({
        timeSlots: updatedTimeSlots,
      });
    } catch (error) {
      console.error('Error updating slot availability:', error);
      throw error;
    }
  },

  async deleteSlot(id: string): Promise<void> {
    try {
      await calendarSlotsCollection.doc(id).delete();
    } catch (error) {
      console.error('Error deleting calendar slot:', error);
      throw error;
    }
  },
};

// Appointments Service - Updated to use dual-database repositories
export const appointmentsService = {
  async getAllAppointments(): Promise<Appointment[]> {
    try {
      await ensureDatabaseInitialized();
      const result = await repositoryManager.appointments.findMany({});
      return result.items;
    } catch (error) {
      console.error('Error getting appointments:', error);
      throw error;
    }
  },

  async getAppointmentById(id: string): Promise<Appointment | null> {
    try {
      const doc = await appointmentsCollection.doc(id).get();
      if (!doc.exists) return null;
      return convertToAppointment(doc);
    } catch (error) {
      console.error('Error getting appointment by id:', error);
      throw error;
    }
  },

  async getAppointmentsByUserId(userId: string): Promise<Appointment[]> {
    try {
      const snapshot = await appointmentsCollection
        .where('userId', '==', userId)
        .where('status', '==', 'active')
        .get();
      return snapshot.docs.map(convertToAppointment);
    } catch (error) {
      console.error('Error getting appointments by userId:', error);
      throw error;
    }
  },

  async getActiveAppointmentsByClient(
    fullName: string,
    dateOfBirth?: string,
  ): Promise<Appointment[]> {
    try {
      const clientsSnapshot = dateOfBirth
        ? await clientsCollection
            .where('fullName', '==', fullName)
            .where('birthday', '==', dateOfBirth)
            .get()
        : await clientsCollection.where('fullName', '==', fullName).get();

      if (clientsSnapshot.empty) {
        return [];
      }

      const clientIds = clientsSnapshot.docs.map(doc => doc.id);

      // Get active appointments for these clients, sorted by date (newest first)
      const appointments: Appointment[] = [];

      for (const clientId of clientIds) {
        const appointmentSnapshot = await appointmentsCollection
          .where('clientId', '==', clientId)
          .where('status', '==', 'active')
          .orderBy('date', 'desc')
          .limit(5)
          .get();

        appointments.push(...appointmentSnapshot.docs.map(convertToAppointment));
      }

      // Sort all appointments by date (newest first) and limit to 5
      return appointments
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 5);
    } catch (error) {
      console.error('Error getting active appointments by client:', error);
      throw error;
    }
  },

  async createAppointment(
    appointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<Appointment> {
    try {
      const id = uuidv4();
      const now = new Date();

      // If clientName is not provided, try to get it from the client record
      let clientName = appointment.clientName;
      if (!clientName && appointment.clientId) {
        const client = await clientsService.getClientById(appointment.clientId);
        if (client) {
          clientName = client.fullName;
        }
      }

      const newAppointment = {
        ...appointment,
        clientName: clientName || '',
        id,
        createdAt: now,
        updatedAt: now,
      };

      await appointmentsCollection.doc(id).set(newAppointment);
      return newAppointment as Appointment;
    } catch (error) {
      console.error('Error creating appointment:', error);
      throw error;
    }
  },

  async updateAppointment(id: string, appointment: Partial<Appointment>): Promise<void> {
    try {
      // If clientId is changing but clientName is not provided, get it from the client
      if (appointment.clientId && !appointment.clientName) {
        const client = await clientsService.getClientById(appointment.clientId);
        if (client) {
          appointment.clientName = client.fullName;
        }
      }

      const updates = {
        ...appointment,
        updatedAt: new Date(),
      };

      await appointmentsCollection.doc(id).update(updates);
    } catch (error) {
      console.error('Error updating appointment:', error);
      throw error;
    }
  },

  async cancelAppointment(id: string): Promise<void> {
    try {
      // Get the appointment to update its slot availability
      const appointment = await this.getAppointmentById(id);
      if (!appointment) {
        throw new Error(`Appointment with id ${id} not found`);
      }

      // Update appointment status
      await appointmentsCollection.doc(id).update({
        status: 'cancelled',
        updatedAt: new Date(),
      });

      // Update calendar slot availability
      await calendarSlotsService.updateSlotAvailability(
        appointment.userId,
        appointment.date,
        appointment.slotId,
        true, // Set the slot as available again
      );
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      throw error;
    }
  },

  async deleteAppointment(id: string): Promise<void> {
    try {
      await appointmentsCollection.doc(id).delete();
    } catch (error) {
      console.error('Error deleting appointment:', error);
      throw error;
    }
  },
};

export const userService = {
  async updateUser(userId: string, payload: Record<string, unknown>): Promise<void> {
    const userDoc = await usersCollection.doc(userId).get();
    if (!userDoc.exists) {
      throw new Error(`User with id ${userId} not found`);
    }

    await usersCollection.doc(userId).update({
      ...userDoc.data(),
      ...payload,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });
  },

  async getUserById(userId: string): Promise<Record<string, unknown>> {
    try {
      const userDoc = await usersCollection.doc(userId).get();
      const userData = userDoc.exists
        ? {
            id: userDoc.id,
            ...userDoc.data(),
            createdAt: userDoc?.data()?.createdAt?.toDate()?.toISOString(),
            updatedAt: userDoc?.data()?.updatedAt?.toDate()?.toISOString(),
          }
        : { id: userId, name: '' };

      return userData;
    } catch (error) {
      console.error('Error getting user by id:', error);
      throw error;
    }
  },

  async getLocationNotifiableStaffIdsForNewAppointment(locationId: string): Promise<string[]> {
    try {
      const snapshot = await usersCollection
        .where('locationIds', 'array-contains', locationId)
        .where('preferences.isAppointmentNotificationsEnabled', '==', true)
        .select('id')
        .get();

      return snapshot.docs.map(doc => doc.id);
    } catch (error) {
      console.error('Error getting staff members:', error);
      throw error;
    }
  },

  async getNotifiableStaffIdsForNewCall(params: {
    clinicId?: number;
    locationId?: string;
  }): Promise<string[]> {
    try {
      const query = Boolean(params.locationId)
        ? usersCollection.where('locationIds', 'array-contains', params.locationId)
        : usersCollection.where('clinicId', '==', params.clinicId);

      const snapshot = await query
        .where('preferences.isIncomingCallNotificationsEnabled', '==', true)
        .select('id')
        .get();

      return snapshot.docs.map(doc => doc.id);
    } catch (error) {
      console.error('Error getting staff members:', error);
      throw error;
    }
  },

  async getNotifiableStaffIdsForNewVoiceMail(params: {
    clinicId?: number;
    locationId?: string;
  }): Promise<string[]> {
    try {
      const query = Boolean(params.locationId)
        ? usersCollection.where('locationIds', 'array-contains', params.locationId)
        : usersCollection.where('clinicId', '==', params.clinicId);

      const snapshot = await query
        .where('preferences.isVoiceMailNotificationsEnabled', '==', true)
        .select('id')
        .get();

      return snapshot.docs.map(doc => doc.id);
    } catch (error) {
      console.error('Error getting staff members:', error);
      throw error;
    }
  },

  async getNotifiableStaffIdsForDailyMonitoring(params: {
    clinicId?: number;
    locationId?: string;
  }): Promise<string[]> {
    try {
      const query = Boolean(params.locationId)
        ? usersCollection.where('locationIds', 'array-contains', params.locationId)
        : usersCollection.where('clinicId', '==', params.clinicId);

      const snapshot = await query
        .where('preferences.isDailyMonitoringNotificationsEnabled', '==', true)
        .select('id')
        .get();

      return snapshot.docs.map(doc => doc.id);
    } catch (error) {
      console.error('Error getting staff members:', error);
      throw error;
    }
  },

  async getNotifiableStaffIdsForHourlyMonitoring(params: {
    clinicId?: number;
    locationId?: string;
  }): Promise<string[]> {
    try {
      const query = Boolean(params.locationId)
        ? usersCollection.where('locationIds', 'array-contains', params.locationId)
        : usersCollection.where('clinicId', '==', params.clinicId);

      const snapshot = await query
        .where('preferences.isHourlyMonitoringNotificationsEnabled', '==', true)
        .select('id')
        .get();

      return snapshot.docs.map(doc => doc.id);
    } catch (error) {
      console.error('Error getting staff members:', error);
      throw error;
    }
  },
};

export const checkFirestoreCollections = async () => {
  try {
    // Check clients collection
    const clientsSnapshot = await clientsCollection.limit(10).get();
    console.log(`Clients collection has ${clientsSnapshot.size} documents`);
    if (clientsSnapshot.size > 0) {
      const sampleClient = clientsSnapshot.docs[0];
      console.log('Sample client data structure:', sampleClient.data());
    }

    // Check calls collection
    const callsSnapshot = await callsCollection.limit(10).get();
    console.log(`Calls collection has ${callsSnapshot.size} documents`);
    if (callsSnapshot.size > 0) {
      const sampleCall = callsSnapshot.docs[0];
      console.log('Sample call data structure:', sampleCall.data());
    }

    return {
      clientsCount: clientsSnapshot.size,
      callsCount: callsSnapshot.size,
    };
  } catch (error) {
    console.error('Error checking Firestore collections:', error);
    throw error;
  }
};

// Migration utility to copy data from patients to clients
export const migratePatientToClientCollection = async () => {
  try {
    const patientsCollection = db.collection('patients');
    console.log('Starting migration from patients to clients collection...');

    // Get all patients
    const patientsSnapshot = await patientsCollection.get();

    if (patientsSnapshot.empty) {
      console.log('No patients to migrate');
      return { migrated: 0 };
    }

    console.log(`Found ${patientsSnapshot.size} patients to migrate`);

    // Copy each patient to clients collection
    let migrated = 0;
    const batch = db.batch();

    patientsSnapshot.forEach(doc => {
      const patientData = doc.data();
      const clientRef = clientsCollection.doc(doc.id);
      batch.set(clientRef, patientData);
      migrated++;
    });

    await batch.commit();
    console.log(`Successfully migrated ${migrated} patients to clients collection`);

    return { migrated };
  } catch (error) {
    console.error('Error migrating patients to clients:', error);
    throw error;
  }
};

// Service for Location
export const locationsService = {
  async getAllLocations(): Promise<Location[]> {
    try {
      const snapshot = await locationsCollection.get();
      return snapshot.docs.map(convertToLocation);
    } catch (error) {
      console.error('Error getting locations:', error);
      throw error;
    }
  },

  async getLocationById(id: string): Promise<Location | null> {
    try {
      const doc = await locationsCollection.doc(id).get();
      if (!doc.exists) return null;
      return convertToLocation(doc);
    } catch (error) {
      console.error('Error getting location:', error);
      throw error;
    }
  },

  async getLocationsByClinicId(clinicId: number): Promise<Location[]> {
    try {
      const snapshot = await locationsCollection.where('clinicId', '==', clinicId).get();
      return snapshot.docs.map(convertToLocation);
    } catch (error) {
      console.error('Error getting locations by clinic:', error);
      throw error;
    }
  },

  async createLocation(
    location: Omit<Location, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<Location> {
    try {
      const id = uuidv4();
      const now = new Date();
      const newLocation = {
        ...location,
        id,
        createdAt: now,
        updatedAt: now,
      };
      await locationsCollection.doc(id).set(newLocation);
      return newLocation as Location;
    } catch (error) {
      console.error('Error creating location:', error);
      throw error;
    }
  },

  async updateLocation(id: string, location: Partial<Location>): Promise<void> {
    try {
      const updates = {
        ...location,
        updatedAt: new Date(),
      };
      await locationsCollection.doc(id).update(updates);
    } catch (error) {
      console.error('Error updating location:', error);
      throw error;
    }
  },

  async deleteLocation(id: string): Promise<void> {
    try {
      await locationsCollection.doc(id).delete();
    } catch (error) {
      console.error('Error deleting location:', error);
      throw error;
    }
  },
};

// Service for CallDetail
export const callDetailsService = {
  async getCallDetailByCallId(callId: string): Promise<CallDetail | null> {
    try {
      const snapshot = await callDetailsCollection.where('callId', '==', callId).limit(1).get();
      if (snapshot.empty) return null;
      return convertToCallDetail(snapshot.docs[0]);
    } catch (error) {
      console.error('Error getting call detail:', error);
      throw error;
    }
  },

  async createCallDetail(
    callDetail: Omit<CallDetail, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<CallDetail> {
    try {
      const id = uuidv4();
      const now = new Date();

      // Make sure no undefined values in callDetail
      const sanitizedCallDetail = {
        callId: callDetail.callId,
        summary: callDetail.summary || '',
        transcription: callDetail.transcription || '',
      };

      const newCallDetail = {
        ...sanitizedCallDetail,
        id,
        createdAt: now,
        updatedAt: now,
      };

      await callDetailsCollection.doc(id).set(newCallDetail);
      return newCallDetail as CallDetail;
    } catch (error) {
      console.error('Error creating call detail:', error);
      throw error;
    }
  },

  async updateCallDetail(id: string, callDetail: Partial<CallDetail>): Promise<void> {
    try {
      // Sanitize the callDetail to avoid undefined values
      const sanitizedUpdates: Record<string, unknown> = {};

      // Only include defined properties and provide defaults for common fields
      if (callDetail.callId !== undefined) sanitizedUpdates.callId = callDetail.callId;
      if (callDetail.summary !== undefined) sanitizedUpdates.summary = callDetail.summary || '';
      if (callDetail.voicemailSummary !== undefined)
        sanitizedUpdates.voicemailSummary = callDetail.voicemailSummary || '';
      if (callDetail.transcription !== undefined)
        sanitizedUpdates.transcription = callDetail.transcription || '';
      if (callDetail.transcriptionWithAudio !== undefined)
        sanitizedUpdates.transcriptionWithAudio = callDetail.transcriptionWithAudio || '';

      // Always update the timestamp
      sanitizedUpdates.updatedAt = new Date();

      await callDetailsCollection.doc(id).update(sanitizedUpdates);
    } catch (error) {
      console.error('Error updating call detail:', error);
      throw error;
    }
  },

  async deleteCallDetail(id: string): Promise<void> {
    try {
      await callDetailsCollection.doc(id).delete();
    } catch (error) {
      console.error('Error deleting call detail:', error);
      throw error;
    }
  },
};

// Service for CallSession
export const callSessionsService = {
  async getCallSessionBySessionId(sessionId: string): Promise<CallSession | null> {
    try {
      const snapshot = await callSessionsCollection
        .where('sessionId', '==', sessionId)
        .limit(1)
        .get();
      if (snapshot.empty) return null;
      return convertToCallSession(snapshot.docs[0]);
    } catch (error) {
      console.error('Error getting call session:', error);
      throw error;
    }
  },

  async getCallSessionById(id: string): Promise<CallSession | null> {
    try {
      const doc = await callSessionsCollection.doc(id).get();
      if (!doc.exists) return null;
      return convertToCallSession(doc);
    } catch (error) {
      console.error('Error getting call session:', error);
      throw error;
    }
  },

  async createCallSession(
    callSession: Omit<CallSession, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<CallSession> {
    try {
      const id = uuidv4();
      const now = new Date();
      const newCallSession = {
        ...callSession,
        id,
        createdAt: now,
        updatedAt: now,
      };
      await callSessionsCollection.doc(id).set(newCallSession);
      return newCallSession as CallSession;
    } catch (error) {
      console.error('Error creating call session:', error);
      throw error;
    }
  },

  async updateCallSession(id: string, callSession: Partial<CallSession>): Promise<void> {
    try {
      await callSessionsCollection.doc(id).update({
        ...callSession,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating call session:', error);
      throw error;
    }
  },

  async findCallSessionsByPhone(phoneNumber: string): Promise<CallSession[]> {
    try {
      // Normalize the phone number by removing non-digit characters
      const normalizedPhoneNumber = phoneNumber.replace(/\D/g, '');

      // Query for call sessions with matching phone number
      const snapshot = await callSessionsCollection
        .where('callerPhone', '==', normalizedPhoneNumber)
        .orderBy('updatedAt', 'desc') // Most recent first
        .limit(5) // Limit to 5 recent sessions
        .get();

      if (snapshot.empty) {
        // If exact match not found, try with endsWith for partial matches
        // This handles cases where the phone format is different (with/without country code)
        const allSessionsSnapshot = await callSessionsCollection
          .where('callerPhone', '!=', '')
          .orderBy('callerPhone')
          .orderBy('updatedAt', 'desc')
          .limit(100) // Get a reasonable number to filter through
          .get();

        const lastDigits = normalizedPhoneNumber.slice(-10); // Get last 10 digits

        // Filter sessions where the phone number ends with the same last digits
        const matchingSessions = allSessionsSnapshot.docs
          .filter(doc => {
            const sessionPhone = doc.data().callerPhone || '';
            const normalizedSessionPhone = sessionPhone.replace(/\D/g, '');
            return normalizedSessionPhone.endsWith(lastDigits);
          })
          .map(doc => convertToCallSession(doc))
          .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()); // Sort by most recent

        return matchingSessions.slice(0, 5); // Return top 5
      }

      return snapshot.docs.map(doc => convertToCallSession(doc));
    } catch (error) {
      console.error('Error finding call sessions by phone:', error);
      throw error;
    }
  },

  async addOrUpdateCallSession(
    sessionId: string,
    data: {
      triggerEvent?: string;
      hasVoiceMail?: boolean;
      callType?: number;
      callTypes?: number[];
      callerPhone?: string;
      patientId?: string;
      agentId?: string;
      appointmentId?: string;
      isRedirected?: boolean;
      callId?: string;
      status?: string;
      telephonyCallId?: string;
      sendNewPatientForm?: boolean;
    },
  ): Promise<CallSession> {
    try {
      // First, try to find the existing session
      const existingSession = await this.getCallSessionBySessionId(sessionId);

      if (existingSession) {
        // Update the existing session with the new data
        const updateData = Object.entries(data).reduce(
          (acc, [key, value]) => {
            if (value !== undefined) {
              acc[key] = value;
            }
            return acc;
          },
          {} as Record<string, unknown>,
        );

        await callSessionsCollection.doc(existingSession.id).update(updateData);
        return this.getCallSessionById(existingSession.id) as Promise<CallSession>;
      } else {
        // Create new session with safe defaults
        const newSessionData: Record<string, unknown> = {
          sessionId,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };

        // Only add properties that are not undefined
        if (data.hasVoiceMail !== undefined) {
          newSessionData.hasVoiceMail = data.hasVoiceMail;
        }

        if (data.callType !== undefined) {
          newSessionData.callType = data.callType;
        }

        if (data.callTypes !== undefined) {
          newSessionData.callTypes = data.callTypes;
        }

        if (data.callerPhone !== undefined) {
          newSessionData.callerPhone = data.callerPhone;
        }

        if (data.patientId !== undefined) {
          newSessionData.patientId = data.patientId;
        }

        if (data.appointmentId !== undefined) {
          newSessionData.appointmentId = data.appointmentId;
        }

        if (data.agentId !== undefined) {
          newSessionData.agentId = data.agentId;
        }

        if (data.isRedirected !== undefined) {
          newSessionData.isRedirected = data.isRedirected;
        }

        if (data.callId !== undefined) {
          newSessionData.callId = data.callId;
        }

        if (data.triggerEvent !== undefined) {
          newSessionData.triggerEvent = data.triggerEvent;
        }

        if (data.status !== undefined) {
          newSessionData.status = data.status;
        }

        if (data.telephonyCallId !== undefined) {
          newSessionData.telephonyCallId = data.telephonyCallId;
        }

        if (data.sendNewPatientForm !== undefined) {
          newSessionData.sendNewPatientForm = data.sendNewPatientForm;
        }

        const docRef = await callSessionsCollection.add(newSessionData);
        return this.getCallSessionById(docRef.id) as Promise<CallSession>;
      }
    } catch (error) {
      console.error('Error adding or updating call session:', error);
      throw error;
    }
  },

  async deleteCallSession(id: string): Promise<void> {
    try {
      await callSessionsCollection.doc(id).delete();
    } catch (error) {
      console.error('Error deleting call session:', error);
      throw error;
    }
  },

  async findCallSessionsByAppointmentId(appointmentId: string): Promise<CallSession[]> {
    try {
      const snapshot = await callSessionsCollection
        .where('appointmentId', '==', appointmentId)
        .get();

      return snapshot.docs.map(doc => convertToCallSession(doc));
    } catch (error) {
      console.error('Error finding call sessions by appointment ID:', error);
      throw error;
    }
  },

  async findCallSessionsByPatientId(patientId: string): Promise<CallSession[]> {
    try {
      const snapshot = await callSessionsCollection
        .where('patientId', '==', patientId)
        .orderBy('updatedAt', 'desc')
        .limit(5) // Limit to 5 most recent sessions
        .get();

      return snapshot.docs.map(doc => convertToCallSession(doc));
    } catch (error) {
      console.error('Error finding call sessions by patient ID:', error);
      throw error;
    }
  },
};

const firestoreServices = {
  clientsService,
  callsService,
  staffCollection,
  checkFirestoreCollections,
  migratePatientToClientCollection,
  calendarSlotsService,
  appointmentsService,
  locationsService,
  callDetailsService,
  callSessionsService,
  mailService,
  userService,
  otpService,
};

export default firestoreServices;
