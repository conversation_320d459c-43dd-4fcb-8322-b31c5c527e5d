{"uid": "dbU2OpciLpVQXZTkh0ZlmNc2NwM2", "oldEmail": "<EMAIL>", "newEmail": "<EMAIL>", "displayName": "test-user-77", "customClaims": {"role": "STAFF", "clinicId": 1}, "firestoreData": {"email": "<EMAIL>", "name": "test-user-77", "role": "STAFF", "specialty": null, "canTakeAppointments": false, "createdAt": {"_seconds": 1746015546, "_nanoseconds": 583000000}, "clinicId": 12, "practiceIds": ["12438ca6-ebe3-4728-9997-d25b6662d5d9"], "locationIds": ["118"], "currentLocationId": "118", "updatedAt": {"_seconds": 1748619896, "_nanoseconds": 28000000}, "preferences": {"isVoiceMailNotificationsEnabled": true, "isIncomingCallNotificationsEnabled": false, "isAppointmentNotificationsEnabled": false, "isDailyMonitoringNotificationsEnabled": false, "isHourlyMonitoringNotificationsEnabled": false}}, "timestamp": "2025-08-01T12:08:30.467Z"}