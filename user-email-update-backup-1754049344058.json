{"uid": "p9U9Y8yaYDhgfIfSEnBQOkQNmH32", "oldEmail": "<EMAIL>", "newEmail": "d<PERSON><PERSON>@frontdesk.doctor", "displayName": "Dmitry", "customClaims": {"role": "STAFF", "clinicId": 12}, "firestoreData": {"email": "<EMAIL>", "name": "Dmitry", "clinicId": 12, "specialty": null, "canTakeAppointments": false, "createdAt": {"_seconds": 1751886058, "_nanoseconds": 524000000}, "locationIds": ["118"], "role": "CLINIC_ADMIN", "preferences": {"isAppointmentNotificationsEnabled": false, "isIncomingCallNotificationsEnabled": false, "isVoiceMailNotificationsEnabled": false, "isDailyMonitoringNotificationsEnabled": true, "isHourlyMonitoringNotificationsEnabled": true}, "updatedAt": {"_seconds": 1751890949, "_nanoseconds": 451000000}}, "timestamp": "2025-08-01T11:55:44.058Z"}