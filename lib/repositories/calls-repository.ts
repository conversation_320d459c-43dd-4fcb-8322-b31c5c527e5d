import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

import { BaseRepository } from '../database/base-repository';
import { Call } from '@/models/Call';
import { CallType } from '@/models/CallTypes';
import { Find<PERSON>rite<PERSON>, PaginatedResult } from '../database/unit-of-work';
import { mysqlService } from '../database/mysql-service';
import logger from '../external-api/v2/utils/logger';
import { AnalyticsFilterOptions } from '../services/dashboard-metrics-service';
import { OfficeHoursService } from '../services/office-hours';
import { LocationService } from '../services/locationService';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Utility function to parse call_types from database.
 * Handles both JSON array format "[4,9]" and CSV format "4,9".
 */
function parseCallTypes(callTypesValue: unknown): number[] {
  if (!callTypesValue) {
    return [];
  }

  try {
    // Convert to string if it's not already
    const callTypesStr = String(callTypesValue);

    // Handle both JSON array format "[4,9]" and CSV format "4,9"
    if (callTypesStr.startsWith('[') && callTypesStr.endsWith(']')) {
      // JSON array format: "[4,9]"
      const parsed = JSON.parse(callTypesStr);
      return Array.isArray(parsed)
        ? parsed.filter(type => typeof type === 'number' && !isNaN(type))
        : [];
    } else {
      // CSV format: "4,9"
      return callTypesStr
        .split(',')
        .map((s: string) => parseInt(s.trim(), 10))
        .filter(type => !isNaN(type));
    }
  } catch (error) {
    console.warn(
      `🔍 Failed to parse call_types: ${callTypesValue} (type: ${typeof callTypesValue})`,
      error,
    );
    return [];
  }
}

/**
 * Phone number filtering interface
 */
interface PhoneSearchParams {
  searchTerm: string;
  normalizedSearchTerm: string;
  searchTokens: string[];
}

/**
 * Advanced call filtering parameters
 */
export interface CallFilterParams {
  limit: number;
  offset?: number;
  clinicId?: number;
  locationId?: number;
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
  callType?: CallType;
  callTypes?: CallType[];
  minPriority?: number;
  maxPriority?: number;
  callDirection?: 'inbound' | 'outbound' | 'both';
  agentId?: string;
  includeUnknownPhoneNumbers?: boolean;
  afterId?: string; // For cursor-based pagination
  createdAt?: {
    startDate?: Date | string;
    endDate?: Date | string;
  };
  excludeZeroDuration?: boolean;
  excludeNoTranscription?: boolean;
  officeHoursOnly?: boolean; // Filter to show only calls during office hours
}

/**
 * Calls Repository - handles dual-database operations for Call entities
 */
export class CallsRepository extends BaseRepository<Call> {
  protected tableName = 'calls';
  protected collectionName = 'calls';

  constructor() {
    super('calls', 'calls', {
      id: 'id',
      clientId: 'client_id',
      clientName: 'client_name',
      userId: 'user_id',
      clinicId: 'clinic_id',
      locationId: 'location_id',
      date: 'call_date',
      reason: 'reason',
      summary: 'summary',
      voicemailSummary: 'voicemail_summary',
      transcription: 'transcription',
      recordingUrl: 'recording_url',
      notes: 'notes',
      priorityScore: 'priority_score',
      urgent: 'is_urgent',
      tags: 'tags',
      phoneNumber: 'phone_number',
      sessionId: 'session_id',
      agentId: 'agent_id',
      hasVoiceMail: 'has_voicemail',
      isOutboundCall: 'is_outbound_call',
      voicemailUrl: 'voicemail_url',
      transcriptionWithAudio: 'transcription_with_audio',
      duration: 'duration',
      type: 'call_type',
      callTypes: 'call_types',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      lastAppointmentId: 'last_appointment_id',
      lastAppointmentDate: 'last_appointment_date',
      lastAppointmentPractitionerId: 'last_appointment_practitioner_id',
      lastAppointmentPractitionerName: 'last_appointment_practitioner_name',
    });
  }

  /**
   * Schema mapping for MySQL columns
   */
  protected getSchemaMapping(): Record<string, string> {
    return {
      id: 'id',
      clientId: 'client_id',
      clientName: 'client_name',
      userId: 'user_id',
      clinicId: 'clinic_id',
      locationId: 'location_id',
      date: 'call_date',
      reason: 'reason',
      summary: 'summary',
      voicemailSummary: 'voicemail_summary',
      transcription: 'transcription',
      recordingUrl: 'recording_url',
      notes: 'notes',
      priorityScore: 'priority_score',
      urgent: 'is_urgent',
      tags: 'tags',
      phoneNumber: 'phone_number',
      sessionId: 'session_id',
      agentId: 'agent_id',
      hasVoiceMail: 'has_voicemail',
      isOutboundCall: 'is_outbound_call',
      voicemailUrl: 'voicemail_url',
      transcriptionWithAudio: 'transcription_with_audio',
      duration: 'duration',
      type: 'call_type',
      callTypes: 'call_types',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      lastAppointmentId: 'last_appointment_id',
      lastAppointmentDate: 'last_appointment_date',
      lastAppointmentPractitionerId: 'last_appointment_practitioner_id',
      lastAppointmentPractitionerName: 'last_appointment_practitioner_name',
    };
  }

  /**
   * Entity validation
   */
  public validateEntity(entity: Partial<Call>): boolean {
    const errors: string[] = [];

    if (
      entity.clientId !== undefined &&
      (!entity.clientId || entity.clientId.trim().length === 0)
    ) {
      errors.push('Client ID is required');
    }

    if (entity.userId !== undefined && (!entity.userId || entity.userId.trim().length === 0)) {
      errors.push('User ID is required');
    }

    if (entity.clinicId !== undefined && (!entity.clinicId || entity.clinicId <= 0)) {
      errors.push('Valid clinic ID is required');
    }

    if (entity.locationId !== undefined && (!entity.locationId || entity.locationId <= 0)) {
      errors.push('Valid location ID is required');
    }

    if (entity.date !== undefined && !entity.date) {
      errors.push('Date is required');
    }

    if (
      entity.recordingUrl !== undefined &&
      (!entity.recordingUrl || entity.recordingUrl.trim().length === 0)
    ) {
      errors.push('Recording URL is required');
    }

    if (errors.length > 0) {
      throw new Error(`Call validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Convert MySQL row to Call entity
   */
  protected mysqlRowToEntity(row: Record<string, unknown>): Call {
    // Handle date conversion properly
    let callDate = new Date();
    if (row.call_date) {
      if (row.call_date instanceof Date) {
        callDate = row.call_date;
      } else if (typeof row.call_date === 'string') {
        callDate = new Date(row.call_date);
      }
    }

    // Handle JSON tags safely
    let parsedTags: string[] | undefined;
    if (row.tags && typeof row.tags === 'string') {
      try {
        parsedTags = JSON.parse(row.tags);
      } catch {
        // If JSON parsing fails, treat as undefined
        parsedTags = undefined;
      }
    } else if (Array.isArray(row.tags)) {
      parsedTags = row.tags;
    }

    return {
      id: row.id as string,
      clientId: row.client_id as string,
      clientName: (row.client_name as string) || '',
      userId: row.user_id as string,
      clinicId: row.clinic_id as number,
      locationId: row.location_id as number,
      date: callDate,
      reason: (row.reason as string) || '',
      summary: (row.summary as string) || '',
      voicemailSummary: (row.voicemail_summary as string) || '',
      transcription: (row.transcription as string) || '',
      recordingUrl: (row.recording_url as string) || '',
      notes: (row.notes as string) || '',
      priorityScore: (row.priority_score as number) || 0,
      urgent: Boolean(row.is_urgent),
      tags: parsedTags || [],
      phoneNumber: (row.phone_number as string) || '',
      sessionId: (row.session_id as string) || '',
      agentId: (row.agent_id as string) || '',
      hasVoiceMail: Boolean(row.has_voicemail),
      isOutboundCall: Boolean(row.is_outbound_call),
      voicemailUrl: (row.voicemail_url as string) || '',
      transcriptionWithAudio: (row.transcription_with_audio as string) || '',
      duration: (row.duration as string) || '',
      type: (row.call_type as number) || 0,
      callTypes: (() => {
        const raw = row.call_types;
        if (!raw) return [(row.call_type as number) || 0];
        if (Array.isArray(raw)) return raw as number[];
        if (typeof raw === 'string') {
          try {
            const arr = JSON.parse(raw);
            if (Array.isArray(arr)) return arr as number[];
          } catch {}
        }
        return [(row.call_type as number) || 0];
      })(),
      lastAppointmentId: (row.last_appointment_id as string) || '',
      lastAppointmentDate: row.last_appointment_date
        ? new Date(row.last_appointment_date as string)
        : null,
      lastAppointmentPractitionerId: (row.last_appointment_practitioner_id as string) || '',
      lastAppointmentPractitionerName: (row.last_appointment_practitioner_name as string) || '',
    };
  }

  /**
   * Convert Call entity to MySQL data
   */
  protected entityToMysqlData(entity: Call): Record<string, unknown> {
    return {
      id: entity.id,
      client_id: entity.clientId,
      client_name: entity.clientName || null,
      user_id: entity.userId,
      clinic_id: entity.clinicId,
      location_id: entity.locationId,
      call_date: entity.date,
      reason: entity.reason || null,
      summary: entity.summary || null,
      voicemail_summary: entity.voicemailSummary || null,
      transcription: entity.transcription || null,
      recording_url: entity.recordingUrl,
      notes: entity.notes || null,
      priority_score: entity.priorityScore || null,
      is_urgent: entity.urgent || false,
      tags: entity.tags ? JSON.stringify(entity.tags) : null,
      phone_number: entity.phoneNumber || null,
      session_id: entity.sessionId || null,
      agent_id: entity.agentId || null,
      has_voicemail: entity.hasVoiceMail || false,
      is_outbound_call: entity.isOutboundCall || false,
      voicemail_url: entity.voicemailUrl || null,
      transcription_with_audio: entity.transcriptionWithAudio || null,
      duration: entity.duration || null,
      call_type: entity.type || null,
      call_types: entity.callTypes ? JSON.stringify(entity.callTypes) : null,
      last_appointment_id: entity.lastAppointmentId || null,
      last_appointment_date: entity.lastAppointmentDate || null,
      last_appointment_practitioner_id: entity.lastAppointmentPractitionerId || null,
      last_appointment_practitioner_name: entity.lastAppointmentPractitionerName || null,
      created_at: new Date(),
      updated_at: new Date(),
    };
  }

  /**
   * Call-specific query methods
   */

  /**
   * Find calls by client ID
   */
  async findByClientId(clientId: string, clinicId?: number): Promise<PaginatedResult<Call>> {
    const criteria: FindCriteria<Call> = {
      where: { clientId },
      orderBy: [{ field: 'date', direction: 'desc' }],
    };

    if (clinicId) {
      criteria.where = { ...criteria.where, clinicId };
    }

    return this.findMany(criteria);
  }

  /**
   * Find calls by clinic ID
   */
  async findByClinicId(clinicId: number): Promise<PaginatedResult<Call>> {
    return this.findMany({
      where: { clinicId },
      orderBy: [{ field: 'date', direction: 'desc' }],
    });
  }

  /**
   * Find calls by session ID
   */
  async findBySessionId(sessionId: string): Promise<PaginatedResult<Call>> {
    return this.findMany({
      where: { sessionId },
      orderBy: [{ field: 'date', direction: 'desc' }],
    });
  }

  /**
   * Get call statistics
   */
  async getCallStats(): Promise<{
    totalCalls: number;
    callsByClinic: Record<number, number>;
    urgentCalls: number;
    voicemailCalls: number;
  }> {
    const result = await this.findMany({});

    const stats = {
      totalCalls: result.items.length,
      callsByClinic: {} as Record<number, number>,
      urgentCalls: 0,
      voicemailCalls: 0,
    };

    result.items.forEach(call => {
      // Count by clinic
      stats.callsByClinic[call.clinicId] = (stats.callsByClinic[call.clinicId] || 0) + 1;

      // Count urgent calls
      if (call.urgent) {
        stats.urgentCalls++;
      }

      // Count voicemail calls
      if (call.hasVoiceMail) {
        stats.voicemailCalls++;
      }
    });

    return stats;
  }

  /**
   * Advanced call filtering and pagination - MySQL implementation
   * Replaces the Firestore-based fetchAndFilterCalls logic
   */
  async fetchAndFilterCalls(params: CallFilterParams): Promise<{
    calls: Call[];
    hasMore: boolean;
    lastId?: string;
  }> {
    const startTime = Date.now();

    try {
      logger.info(
        { context: 'CallsRepository.fetchAndFilterCalls', params },
        'Starting MySQL call filtering',
      );

      const {
        limit,
        offset = 0,
        clinicId,
        locationId,
        startDate,
        endDate,
        searchTerm,
        callType,
        callTypes,
        minPriority,
        maxPriority,
        callDirection = 'inbound',
        agentId,
        includeUnknownPhoneNumbers = false,
        afterId,
        createdAt,
        excludeZeroDuration,
        excludeNoTranscription,
        officeHoursOnly = false,
      } = params;

      // Validate and prepare phone search if needed
      let phoneSearch: PhoneSearchParams | null = null;
      if (searchTerm && searchTerm.trim()) {
        phoneSearch = this.preparePhoneSearch(searchTerm.trim());
      }

      // Get environment variables for filtering
      const configuredAgentId = process.env.GCP_AGENT_ID;
      const phoneBlacklistEnv = process.env.PHONE_NUMBER_BLACKLIST;
      const phoneBlacklist = phoneBlacklistEnv
        ? phoneBlacklistEnv.split(',').map(p => p.trim())
        : ['9178582585', '9082740595', '9176268074', '7328229112']; // ALL 4 REQUIRED BLACKLISTED NUMBERS

      // If office hours filtering is enabled, we need to fetch more records and filter iteratively
      if (officeHoursOnly) {
        return this.fetchAndFilterCallsWithOfficeHours({
          limit,
          offset,
          clinicId,
          locationId,
          startDate,
          endDate,
          callType,
          callTypes,
          callDirection,
          minPriority,
          maxPriority,
          phoneSearch,
          includeUnknownPhoneNumbers,
          phoneBlacklist,
          configuredAgentId: agentId || configuredAgentId,
          afterId,
          createdAt,
          excludeZeroDuration,
          excludeNoTranscription,
        });
      }

      // Standard path without office hours filtering
      const queryBuilder = this.buildCallsQuery({
        clinicId,
        locationId,
        startDate,
        endDate,
        callType,
        callTypes,
        createdAt,
        callDirection,
        minPriority,
        maxPriority,
        phoneSearch,
        includeUnknownPhoneNumbers,
        phoneBlacklist,
        configuredAgentId: agentId || configuredAgentId,
        limit: limit + 1, // Get one extra to check for more results
        offset,
        afterId,
        excludeZeroDuration,
        excludeNoTranscription,
      });

      // Execute the query with detailed debugging
      logger.info(
        {
          context: 'CallsRepository.fetchAndFilterCalls',
          sqlQuery: queryBuilder.sql,
          paramCount: queryBuilder.params.length,
          paramTypes: queryBuilder.params.map(p => typeof p),
          allParams: queryBuilder.params, // Log ALL parameters for debugging
          questionMarks: (queryBuilder.sql.match(/\?/g) || []).length,
        },
        'About to execute MySQL query',
      );

      // Count question marks in SQL vs parameter count
      const questionMarkCount = (queryBuilder.sql.match(/\?/g) || []).length;
      if (questionMarkCount !== queryBuilder.params.length) {
        const errorMsg = `Parameter count mismatch: SQL has ${questionMarkCount} placeholders but ${queryBuilder.params.length} parameters provided`;
        logger.error(
          {
            context: 'CallsRepository.fetchAndFilterCalls',
            sqlQuery: queryBuilder.sql,
            paramCount: queryBuilder.params.length,
            questionMarkCount,
            allParams: queryBuilder.params,
          },
          errorMsg,
        );
        throw new Error(errorMsg);
      }

      // Validate all parameters are not undefined or invalid
      const invalidParams = queryBuilder.params
        .map((param, index) => ({ index, param, type: typeof param }))
        .filter(
          ({ param }) =>
            param === undefined ||
            (param !== null && param !== '' && param !== 0 && param !== false && !param),
        );

      if (invalidParams.length > 0) {
        const errorMsg = `Invalid parameters detected: ${JSON.stringify(invalidParams)}`;
        logger.error(
          {
            context: 'CallsRepository.fetchAndFilterCalls',
            invalidParams,
            allParams: queryBuilder.params,
          },
          errorMsg,
        );
        throw new Error(errorMsg);
      }

      const results = await mysqlService.query<Record<string, unknown>>(
        queryBuilder.sql,
        queryBuilder.params,
      );

      logger.info(
        {
          context: 'CallsRepository.fetchAndFilterCalls',
          resultCount: results.length,
          duration: Date.now() - startTime,
        },
        'MySQL query executed successfully',
      );

      // Debug: log sample of returned records with dates
      if (results.length > 0) {
        console.log('🔍 Sample returned records:');
        results.slice(0, 3).forEach((row, index) => {
          console.log(`  Record ${index + 1}: id=${row.id}, call_date=${row.call_date}`);
        });
      }

      // Process results
      const calls = results.slice(0, limit).map(row => this.mysqlRowToEntity(row));
      const hasMore = results.length > limit;
      const lastId = calls.length > 0 ? calls[calls.length - 1].id : undefined;

      return {
        calls,
        hasMore,
        lastId,
      };
    } catch (error) {
      logger.error(
        {
          context: 'CallsRepository.fetchAndFilterCalls',
          error: error instanceof Error ? error.message : String(error),
          duration: Date.now() - startTime,
        },
        'MySQL call filtering failed',
      );
      throw error;
    }
  }

  /**
   * Safely substitute parameters into SQL query with proper escaping
   * This eliminates parameter binding issues by constructing a fully-formed SQL query
   */
  private substituteParameters(sql: string, params: unknown[]): string {
    let result = sql;
    let paramIndex = 0;

    // Replace each ? with the corresponding escaped parameter value
    result = result.replace(/\?/g, () => {
      if (paramIndex >= params.length) {
        throw new Error(
          `Not enough parameters provided. Expected at least ${paramIndex + 1} parameters.`,
        );
      }

      const param = params[paramIndex++];
      return this.escapeValue(param);
    });

    if (paramIndex !== params.length) {
      console.warn(
        `Parameter count mismatch: used ${paramIndex} parameters but ${params.length} were provided`,
      );
    }

    return result;
  }

  /**
   * Escape a value for safe inclusion in SQL query
   */
  private escapeValue(value: unknown): string {
    if (value === null || value === undefined) {
      return 'NULL';
    }

    if (typeof value === 'string') {
      // Escape single quotes by doubling them, and wrap in single quotes
      return `'${value.replace(/'/g, "''")}'`;
    }

    if (typeof value === 'number') {
      // Numbers don't need escaping
      return String(value);
    }

    if (typeof value === 'boolean') {
      // Convert boolean to 1/0 for MySQL
      return value ? '1' : '0';
    }

    if (value instanceof Date) {
      // Format date as MySQL datetime string
      return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
    }

    // For any other type, convert to string and escape
    return `'${String(value).replace(/'/g, "''")}'`;
  }

  /**
   * Prepare phone search parameters
   */
  private preparePhoneSearch(searchTerm: string): PhoneSearchParams {
    const normalizedSearchTerm = this.normalizePhoneNumber(searchTerm);
    const searchTokens = this.generatePhoneSearchTokens(normalizedSearchTerm);

    return {
      searchTerm,
      normalizedSearchTerm,
      searchTokens,
    };
  }

  /**
   * Normalize phone number for searching
   */
  private normalizePhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace(/\D/g, ''); // Remove all non-digit characters
  }

  /**
   * Generate phone search tokens for partial matching
   */
  private generatePhoneSearchTokens(phoneNumber: string): string[] {
    const normalized = this.normalizePhoneNumber(phoneNumber);
    const tokens: string[] = [normalized];

    // Add variations: with +1, without +1, with/without area code, etc.
    if (normalized.length === 11 && normalized.startsWith('1')) {
      tokens.push(normalized.substring(1)); // Remove leading 1
    }
    if (normalized.length === 10) {
      tokens.push(`1${normalized}`); // Add leading 1
    }

    // Add partial matches (last 4, last 7 digits)
    if (normalized.length >= 4) {
      tokens.push(normalized.slice(-4));
    }
    if (normalized.length >= 7) {
      tokens.push(normalized.slice(-7));
    }

    return Array.from(new Set(tokens)); // Remove duplicates
  }

  /**
   * Build complex calls query with all filters
   */
  private buildCallsQuery(params: {
    clinicId?: number;
    locationId?: number;
    startDate?: Date;
    endDate?: Date;
    callType?: CallType;
    callTypes?: CallType[];
    callDirection?: 'inbound' | 'outbound' | 'both';
    minPriority?: number;
    maxPriority?: number;
    phoneSearch?: PhoneSearchParams | null;
    includeUnknownPhoneNumbers?: boolean;
    phoneBlacklist?: string[];
    configuredAgentId?: string;
    limit: number;
    offset: number;
    afterId?: string;
    excludeZeroDuration?: boolean;
    excludeNoTranscription?: boolean;
    createdAt?: {
      startDate?: Date | string;
      endDate?: Date | string;
    };
  }): { sql: string; params: unknown[] } {
    const {
      clinicId,
      locationId,
      startDate,
      endDate,
      callType,
      callTypes,
      callDirection,
      minPriority,
      maxPriority,
      phoneSearch,
      includeUnknownPhoneNumbers,
      phoneBlacklist = [],
      configuredAgentId,
      limit,
      offset,
      afterId,
      createdAt,
      excludeZeroDuration = true,
      excludeNoTranscription,
    } = params;

    // Build SELECT clause with correct column names from actual schema - single line for MySQL compatibility
    const sql =
      'SELECT id, client_id, client_name, user_id, clinic_id, location_id, session_id, agent_id, call_date, phone_number, reason, summary, voicemail_summary, transcription, transcription_with_audio, recording_url, voicemail_url, notes, priority_score, is_urgent, tags, has_voicemail, is_outbound_call, duration, call_type, call_types, last_appointment_id, last_appointment_date, last_appointment_practitioner_id, last_appointment_practitioner_name, created_at, updated_at FROM calls';

    const sqlParams: unknown[] = [];
    const conditions: string[] = [];

    // Phone number filters - most restrictive first
    if (!includeUnknownPhoneNumbers) {
      conditions.push(
        "phone_number IS NOT NULL AND phone_number != '' AND phone_number != 'Unknown'",
      );
    }

    // Phone number blacklist - RE-ENABLED
    if (phoneBlacklist.length > 0) {
      // Use multiple NOT LIKE conditions for better compatibility
      const blacklistConditions = phoneBlacklist.map(() => 'phone_number NOT LIKE ?').join(' AND ');
      const fullBlacklistCondition = `(${blacklistConditions})`;

      conditions.push(fullBlacklistCondition);
      phoneBlacklist.forEach(phoneNumber => {
        sqlParams.push(`%${phoneNumber}%`);
      });
    }

    // Agent ID filter: show calls with no agent_id (legacy) or matching agent_id
    if (configuredAgentId && configuredAgentId.trim() !== '') {
      conditions.push('(agent_id IS NULL OR agent_id = ? OR agent_id = ?)');
      sqlParams.push(''); // Empty string for legacy calls with empty agent_id
      sqlParams.push(configuredAgentId); // Matching agent_id
    }

    // Clinic filter
    if (clinicId !== undefined) {
      conditions.push('clinic_id = ?');
      sqlParams.push(clinicId);
    }

    // Location filter
    if (locationId !== undefined) {
      conditions.push('location_id = ?');
      sqlParams.push(locationId);
    }

    // Date range filters
    if (startDate) {
      conditions.push('call_date >= ?');
      sqlParams.push(startDate.toISOString()); // Convert to UTC string to prevent timezone issues
      console.log('🔍 Repository startDate filter:', startDate.toISOString());
    }

    if (endDate) {
      conditions.push('call_date <= ?');
      sqlParams.push(endDate.toISOString()); // Convert to UTC string to prevent timezone issues
      console.log('🔍 Repository endDate filter:', endDate.toISOString());
    }

    // Created at date range filters
    if (createdAt) {
      const createdAtStart = createdAt.startDate || dayjs.utc().startOf('day').toISOString();
      const createdAtEnd = createdAt.endDate || dayjs.utc().endOf('day').toISOString();

      conditions.push('created_at BETWEEN ? AND ?');
      sqlParams.push(createdAtStart);
      sqlParams.push(createdAtEnd);
      console.log('🔍 Repository createdAt filter:', createdAtStart, 'to', createdAtEnd);
    }

    // Call type filter - support both single and multiple call types
    const typesToFilter =
      callTypes && callTypes.length > 0 ? callTypes : callType !== undefined ? [callType] : [];
    if (typesToFilter.length > 0) {
      if (typesToFilter.length === 1) {
        // Single call type - use existing logic for compatibility
        conditions.push('(call_type = ? OR JSON_CONTAINS(call_types, JSON_ARRAY(?), "$"))');
        sqlParams.push(typesToFilter[0]);
        sqlParams.push(typesToFilter[0]);
      } else {
        // Multiple call types - use IN clause for legacy column and JSON_OVERLAPS for new column
        const placeholders = typesToFilter.map(() => '?').join(', ');
        conditions.push(
          `(call_type IN (${placeholders}) OR JSON_OVERLAPS(call_types, JSON_ARRAY(${placeholders})))`,
        );
        // Add parameters twice - once for IN clause, once for JSON_ARRAY
        sqlParams.push(...typesToFilter);
        sqlParams.push(...typesToFilter);
      }
    }

    // Call direction filter
    if (callDirection !== 'both') {
      if (callDirection === 'outbound') {
        conditions.push('is_outbound_call = 1');
      } else {
        conditions.push('(is_outbound_call = 0 OR is_outbound_call IS NULL)');
      }
    }

    // Priority range filters
    if (minPriority !== undefined) {
      conditions.push('priority_score >= ?');
      sqlParams.push(minPriority);
    }

    if (maxPriority !== undefined) {
      conditions.push('priority_score <= ?');
      sqlParams.push(maxPriority);
    }

    // Phone search (partial matching)
    if (phoneSearch && phoneSearch.searchTokens.length > 0) {
      const phoneConditions = phoneSearch.searchTokens
        .map(() => 'REPLACE(phone_number, "-", "") LIKE ?')
        .join(' OR ');
      conditions.push(`(${phoneConditions})`);
      phoneSearch.searchTokens.forEach(token => {
        sqlParams.push(`%${token}%`);
      });
    }

    if (excludeNoTranscription) {
      conditions.push(
        "transcription IS NOT NULL AND transcription != '' AND transcription_with_audio IS NOT NULL AND transcription_with_audio != ''",
      );
    }

    // Duration-based filtering - exclude calls with ONLY "other" type and exactly 0 minutes duration
    // This filters out connection test calls but preserves legitimate short calls
    if (excludeZeroDuration) {
      conditions.push(`NOT (
      call_type = 0 AND 
      JSON_EXTRACT(call_types, '$') = JSON_ARRAY(0) AND
      (duration = '0 min' OR duration = '0 mins' OR duration = '0 sec' OR duration = '0 secs')
    )`);
    }

    // This filter excludes calls with type "other" and zero duration from the main call display
    // The analytics filter options provide additional control for reports and analytics

    // Cursor-based pagination has been disabled to fix ordering issues
    // We now use offset-based pagination exclusively for consistent date ordering
    if (afterId) {
      // Log that cursor-based pagination is disabled
      console.log('🔍 Cursor-based pagination disabled - using offset-based pagination instead');
      // Don't add any cursor conditions - the service layer handles offset calculation
    }

    // Build final SQL as single line to avoid MySQL parsing issues
    let finalSql = sql;
    if (conditions.length > 0) {
      // CRITICAL FIX: Filter out empty or invalid conditions before joining
      const validConditions = conditions.filter(
        cond =>
          cond &&
          typeof cond === 'string' &&
          cond.trim() !== '' &&
          !cond.includes('undefined') &&
          !cond.startsWith('?') && // Don't allow conditions that start with ?
          !cond.endsWith('AND') && // Don't allow malformed AND
          !cond.endsWith('OR'), // Don't allow malformed OR
      );

      if (validConditions.length > 0) {
        const whereClause = validConditions.join(' AND ');
        finalSql += ` WHERE ${whereClause}`;
      } else {
        console.error('🚨 ALL CONDITIONS WERE INVALID - NO WHERE CLAUSE ADDED');
      }
    }

    // Add ORDER BY for consistent pagination (newest first)
    // To keep a deterministic order we sort by `call_date` and, for identical dates, by `id`, both in DESC order.
    finalSql += ' ORDER BY call_date DESC, id DESC';

    // Add LIMIT
    finalSql += ' LIMIT ?';
    sqlParams.push(limit);

    // Add OFFSET if specified
    if (offset > 0) {
      finalSql += ' OFFSET ?';
      sqlParams.push(offset);
    }

    // Debug logging for date filtering issues
    console.log('🔍 Final SQL query:', finalSql);
    console.log('🔍 SQL parameters:', sqlParams);

    // CRITICAL DEBUGGING: Validate the built SQL before returning
    const builtPhoneRefs = (finalSql.match(/phone_number/g) || []).length;
    const builtCompleteRefs = (finalSql.match(/phone_number\s+(?:IS|!=|=|NOT LIKE|LIKE)/g) || [])
      .length;
    const builtQuestionMarks = (finalSql.match(/\?/g) || []).length;

    // CRITICAL VALIDATION: Check for parameter mismatch
    const parameterMismatch = builtQuestionMarks !== sqlParams.length;

    // Basic logging for monitoring
    if (parameterMismatch) {
      console.error('🚨 Parameter mismatch detected:', {
        questionMarks: builtQuestionMarks,
        paramsLength: sqlParams.length,
      });
    }

    // Remove malformed question mark detection - we'll use direct substitution instead

    // Only check for incomplete phone conditions if we have a significant mismatch
    if (builtPhoneRefs > 0 && builtCompleteRefs < builtPhoneRefs - 1) {
      console.error('🚨 REPOSITORY: Incomplete phone_number condition detected in built SQL!');
      console.error('🚨 Built SQL:', finalSql);
      console.error('🚨 Conditions array:', conditions);
    }

    // Return the parameterised SQL and the corresponding parameters array
    return {
      sql: finalSql,
      params: sqlParams,
    };
  }

  /**
   * Count total calls within an inclusive date range.
   * @param startDate UTC start date
   * @param endDate UTC end date
   * @param filterOptions Optional filters for analytics
   */
  async countCallsInRange(
    startDate: Date,
    endDate: Date,
    filterOptions?: AnalyticsFilterOptions,
  ): Promise<number> {
    const conditions = ['call_date BETWEEN ? AND ?'];
    const params: unknown[] = [startDate, endDate];

    // Add filter conditions if provided
    if (filterOptions) {
      if (filterOptions.excludeZeroDuration) {
        // Exclude all calls with zero duration
        conditions.push(
          `NOT (duration = '0 min' OR duration = '0 mins' OR duration = '0 sec' OR duration = '0 secs' OR duration IS NULL OR duration = '')`,
        );
      }

      if (filterOptions.excludeDisconnected) {
        conditions.push('(call_type != ? AND NOT JSON_CONTAINS(call_types, JSON_ARRAY(?), "$"))');
        params.push(CallType.DISCONNECTED);
        params.push(CallType.DISCONNECTED);
      }
    }

    const sql = `SELECT COUNT(*) as cnt FROM calls WHERE ${conditions.join(' AND ')}`;
    const rows = await mysqlService.query<{ cnt: number }>(sql, params);
    return rows[0]?.cnt ?? 0;
  }

  /**
   * Sum durations (in seconds) of calls within a date range.
   * Duration is stored as varchar so we parse in Node.
   * @returns Total duration in **seconds**.
   */
  async sumDurationsInRange(
    startDate: Date,
    endDate: Date,
    filterOptions?: AnalyticsFilterOptions,
  ): Promise<number> {
    const conditions = ['call_date BETWEEN ? AND ?', 'duration IS NOT NULL'];
    const params: unknown[] = [startDate, endDate];

    // Add filter conditions if provided
    if (filterOptions) {
      if (filterOptions.excludeZeroDuration) {
        // Exclude all calls with zero duration
        conditions.push(
          `NOT (duration = '0 min' OR duration = '0 mins' OR duration = '0 sec' OR duration = '0 secs' OR duration IS NULL OR duration = '')`,
        );
      }

      if (filterOptions.excludeDisconnected) {
        conditions.push('(call_type != ? AND NOT JSON_CONTAINS(call_types, JSON_ARRAY(?), "$"))');
        params.push(CallType.DISCONNECTED);
        params.push(CallType.DISCONNECTED);
      }
    }

    const sql = `SELECT duration FROM calls WHERE ${conditions.join(' AND ')}`;
    const rows = await mysqlService.query<{ duration: string | null }>(sql, params);

    console.log(`🔍 Duration rows found: ${rows.length}`);

    let totalSeconds = 0;
    for (const row of rows) {
      const parsed = this.parseDurationToSeconds(row.duration);
      totalSeconds += parsed;
    }

    console.log(`🔍 Total duration calculated: ${totalSeconds} seconds`);
    return totalSeconds;
  }

  /**
   * Count appointment-related call types within a date range.
   * Now properly handles both single call_type and multiple call_types array.
   * @returns Object with reschedule, newAppointment, and cancel counts.
   */
  async countAppointmentsHandledInRange(
    startDate: Date,
    endDate: Date,
    filterOptions?: AnalyticsFilterOptions,
  ): Promise<{ reschedule: number; newAppointment: number; cancel: number }> {
    const {
      RESCHEDULE,
      NEW_PATIENT_NEW_APPOINTMENT,
      NEW_APPOINTMENT_EXISTING_PATIENT,
      CANCELLATION,
    } = CallType;

    const conditions = ['call_date BETWEEN ? AND ?'];
    const params: unknown[] = [startDate, endDate];

    // Add filter conditions if provided
    if (filterOptions) {
      // Note: NEITHER excludeZeroDuration NOR excludeDisconnected filters are applied to appointment counts
      // because appointments already have meaningful call types (RESCHEDULE, NEW_APPOINTMENT, CANCEL)
      // and should be counted consistently regardless of duration or disconnected status.
      // These filters only apply to general call counts and duration totals.
    }

    // Query to get all calls that have appointment-related types in either call_type or call_types
    const sql = `
      SELECT call_type, call_types
      FROM calls
      WHERE ${conditions.join(' AND ')}
        AND (
          call_type IN (?, ?, ?, ?) OR
          JSON_CONTAINS(call_types, JSON_ARRAY(?), "$") OR
          JSON_CONTAINS(call_types, JSON_ARRAY(?), "$") OR
          JSON_CONTAINS(call_types, JSON_ARRAY(?), "$") OR
          JSON_CONTAINS(call_types, JSON_ARRAY(?), "$")
        )
    `;

    // Add appointment type parameters twice - once for call_type IN clause, once for JSON_CONTAINS
    params.push(
      RESCHEDULE,
      NEW_PATIENT_NEW_APPOINTMENT,
      NEW_APPOINTMENT_EXISTING_PATIENT,
      CANCELLATION,
    );
    params.push(
      RESCHEDULE,
      NEW_PATIENT_NEW_APPOINTMENT,
      NEW_APPOINTMENT_EXISTING_PATIENT,
      CANCELLATION,
    );

    console.log(
      `🔍 Appointment query params: [${startDate.toISOString()}, ${endDate.toISOString()}, appointment types...]`,
    );

    const rows = await mysqlService.query<{
      call_type: number | string;
      call_types: string | null;
    }>(sql, params);

    console.log(`🔍 Appointment rows found: ${rows.length}`);

    const result = { reschedule: 0, newAppointment: 0, cancel: 0 };

    for (const row of rows) {
      // Get all call types for this call (both single and multiple)
      const callTypes = new Set<number>();

      // Add primary call type
      if (row.call_type) {
        const primaryType =
          typeof row.call_type === 'string' ? parseInt(row.call_type, 10) : row.call_type;
        if (!isNaN(primaryType)) {
          callTypes.add(primaryType);
        }
      }

      // Add call types from array
      const parsedTypes = parseCallTypes(row.call_types);
      parsedTypes.forEach(type => callTypes.add(type));

      // Count each appointment type found in this call
      for (const callType of Array.from(callTypes)) {
        switch (callType) {
          case RESCHEDULE:
            result.reschedule++;
            break;
          case NEW_PATIENT_NEW_APPOINTMENT:
          case NEW_APPOINTMENT_EXISTING_PATIENT:
            result.newAppointment++;
            break;
          case CANCELLATION:
            result.cancel++;
            break;
        }
      }
    }

    console.log(`🔍 Appointment result:`, result);
    return result;
  }

  /**
   * Count unique calls that have each call type in the specified date range.
   * Now properly handles both single call_type and multiple call_types array.
   * Each call is counted only ONCE per type, regardless of how many types it has.
   * This ensures the issue counts never exceed the total call count.
   */
  async countAllCallTypesInRange(
    startDate: Date,
    endDate: Date,
    filterOptions?: AnalyticsFilterOptions,
  ): Promise<Record<string, number>> {
    const conditions = ['call_date BETWEEN ? AND ?'];
    const params: unknown[] = [startDate, endDate];

    // Add filter conditions if provided
    if (filterOptions) {
      if (filterOptions.excludeZeroDuration) {
        // Apply same zero duration filter as other methods
        conditions.push(
          `NOT (duration = '0 min' OR duration = '0 mins' OR duration = '0 sec' OR duration = '0 secs' OR duration IS NULL OR duration = '')`,
        );
      }

      if (filterOptions.excludeDisconnected) {
        conditions.push('(call_type != ? AND NOT JSON_CONTAINS(call_types, JSON_ARRAY(?), "$"))');
        params.push(CallType.DISCONNECTED);
        params.push(CallType.DISCONNECTED);
      }
    }

    // Get all calls with their call types
    const sql = `
      SELECT call_type, call_types
      FROM calls
      WHERE ${conditions.join(' AND ')}
    `;

    console.log(
      `🔍 Call types query params: [${startDate.toISOString()}, ${endDate.toISOString()}]`,
    );

    const rows = await mysqlService.query<{
      call_type: number | string | null;
      call_types: string | null;
    }>(sql, params);

    console.log(`🔍 Call type rows found: ${rows.length}`);

    // Track which calls have which types to count unique calls per type
    const typeToCallsMap: Record<string, Set<string>> = {};

    // Process each call and track which types it has
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const callId = `call_${i}`; // Use row index as unique identifier
      const callTypes = new Set<number>();

      // Add primary call type (fallback to 0 if null)
      const primaryType = row.call_type
        ? typeof row.call_type === 'string'
          ? parseInt(row.call_type, 10)
          : row.call_type
        : 0;

      if (!isNaN(primaryType)) {
        callTypes.add(primaryType);
      }

      // Add call types from array (if exists and different from primary)
      const parsedTypes = parseCallTypes(row.call_types);
      parsedTypes.forEach(type => callTypes.add(type));

      // Add this call to each type's set
      for (const callType of Array.from(callTypes)) {
        if (CallType[callType]) {
          const typeName = CallType[callType];
          if (!typeToCallsMap[typeName]) {
            typeToCallsMap[typeName] = new Set();
          }
          typeToCallsMap[typeName].add(callId);
        }
      }
    }

    // Convert sets to counts (unique calls per type)
    const result: Record<string, number> = {};
    for (const [typeName, callsSet] of Object.entries(typeToCallsMap)) {
      result[typeName] = callsSet.size;
    }

    console.log(`🔍 Call types result (unique calls per type):`, result);
    console.log(
      `🔍 Total calls processed: ${rows.length}, Total type counts: ${Object.values(result).reduce((sum, count) => sum + count, 0)}`,
    );

    return result;
  }

  /**
   * Convert various duration string formats to seconds.
   * Supports:
   *   - HH:MM:SS
   *   - MM:SS
   *   - "5 min" / "5 mins" / "3.0 min" / "1.7 min"
   *   - "13 sec" / "13 secs"
   *   - plain seconds number in string form
   */
  private parseDurationToSeconds(durationStr?: string | null): number {
    if (!durationStr) return 0;
    const trimmed = durationStr.trim();

    // HH:MM:SS
    if (/^\d{1,2}:\d{2}:\d{2}$/.test(trimmed)) {
      const [h, m, s] = trimmed.split(':').map(Number);
      return h * 3600 + m * 60 + s;
    }

    // MM:SS
    if (/^\d{1,2}:\d{2}$/.test(trimmed)) {
      const [m, s] = trimmed.split(':').map(Number);
      return m * 60 + s;
    }

    // "5 min" / "5 mins" / "3.0 min" / "1.7 min"
    const minMatch = trimmed.match(/^(\d+(?:\.\d+)?)\s*min(s)?/i);
    if (minMatch) {
      return Math.round(parseFloat(minMatch[1]) * 60);
    }

    // "13 sec" / "13 secs"
    const secMatch = trimmed.match(/^(\d+(?:\.\d+)?)\s*sec(s)?/i);
    if (secMatch) {
      return Math.round(parseFloat(secMatch[1]));
    }

    // Numeric seconds
    const seconds = Number(trimmed);
    return Number.isFinite(seconds) ? seconds : 0;
  }

  /**
   * Apply office hours filtering to calls using location-based office hours
   */
  private async applyOfficeHoursFiltering(
    calls: Call[],
    clinicId?: number,
    locationId?: number,
  ): Promise<Call[]> {
    try {
      // Pre-fetch location data for office hours checking
      let locationData: Awaited<ReturnType<typeof LocationService.getLocationById>> | null = null;
      let locationsByClinic: Record<
        string,
        Awaited<ReturnType<typeof LocationService.getLocationById>>
      > = {};

      if (locationId && clinicId) {
        // Fetch specific location
        locationData = await LocationService.getLocationById(locationId.toString(), clinicId);
        if (!locationData) {
          logger.warn(
            { context: 'applyOfficeHoursFiltering', locationId, clinicId },
            'Location not found for office hours filtering',
          );
        }
      } else if (clinicId) {
        // Fetch all locations for the clinic
        const clinicLocationResult = await LocationService.getLocationsByClinicId(clinicId);
        locationsByClinic = clinicLocationResult.locations.reduce(
          (acc, loc) => {
            acc[String(loc.id)] = loc;
            return acc;
          },
          {} as Record<string, Awaited<ReturnType<typeof LocationService.getLocationById>>>,
        );
      }

      // Filter calls based on office hours
      const filteredCalls = calls.filter(call => {
        let callLocation: Awaited<ReturnType<typeof LocationService.getLocationById>> | null = null;

        if (locationData) {
          // Use pre-fetched specific location
          callLocation = locationData;
        } else if (call.locationId && locationsByClinic[String(call.locationId)]) {
          // Use location from clinic locations lookup
          callLocation = locationsByClinic[String(call.locationId)];
        } else {
          // No location data available, cannot filter by office hours
          logger.debug(
            {
              context: 'applyOfficeHoursFiltering',
              callId: call.id,
              callLocationId: call.locationId,
            },
            'No location data available for office hours filtering',
          );
        }

        if (callLocation) {
          // Only filter if location has actual office hours configured
          if (!callLocation.officeHours || Object.keys(callLocation.officeHours).length === 0) {
            logger.debug(
              {
                context: 'applyOfficeHoursFiltering',
                callId: call.id,
                locationId: callLocation.id,
              },
              'Location has no office hours configured - including call in results',
            );
            return true; // Include call if no office hours configured (can't filter what we don't know)
          }

          // Use the actual office hours from the location
          const timeZone = callLocation.timeZone || 'America/Chicago'; // Default to Central Time
          const status = OfficeHoursService.checkOfficeHours(
            callLocation.officeHours,
            timeZone,
            call.date,
          );

          logger.debug(
            {
              context: 'applyOfficeHoursFiltering',
              callId: call.id,
              locationId: callLocation.id,
              callDate: call.date.toISOString(),
              timeZone,
              isOpen: status.isOpen,
              currentStatus: status.currentStatus,
            },
            'Office hours check completed',
          );

          return status.isOpen;
        } else {
          // No location data available - cannot filter by office hours
          logger.debug(
            {
              context: 'applyOfficeHoursFiltering',
              callId: call.id,
              callLocationId: call.locationId,
            },
            'No location data available - including call in results',
          );
          return true; // Include call if we can't determine location office hours
        }
      });

      return filteredCalls;
    } catch (error) {
      logger.error(
        {
          context: 'applyOfficeHoursFiltering',
          error: error instanceof Error ? error.message : String(error),
        },
        'Error applying office hours filtering, returning all calls',
      );
      return calls; // Return all calls if filtering fails
    }
  }

  /**
   * Fetch and filter calls with office hours filtering - uses iterative fetching
   * to ensure we have enough records after office hours filtering is applied
   */
  private async fetchAndFilterCallsWithOfficeHours(params: {
    limit: number;
    offset: number;
    clinicId?: number;
    locationId?: number;
    startDate?: Date;
    endDate?: Date;
    callType?: CallType;
    callTypes?: CallType[];
    callDirection?: 'inbound' | 'outbound' | 'both';
    minPriority?: number;
    maxPriority?: number;
    phoneSearch?: PhoneSearchParams | null;
    includeUnknownPhoneNumbers?: boolean;
    phoneBlacklist?: string[];
    configuredAgentId?: string;
    afterId?: string;
    createdAt?: {
      startDate?: Date | string;
      endDate?: Date | string;
    };
    excludeZeroDuration?: boolean;
    excludeNoTranscription?: boolean;
  }): Promise<{
    calls: Call[];
    hasMore: boolean;
    lastId?: string;
  }> {
    const startTime = Date.now();
    const { limit, offset } = params;

    try {
      logger.info(
        { context: 'fetchAndFilterCallsWithOfficeHours', params },
        'Starting office hours filtered call fetching',
      );

      const filteredCalls: Call[] = [];
      let currentOffset = offset;
      let hasMore = false;
      const maxIterations = 5; // Prevent infinite loops
      let iteration = 0;

      // Fetch batches until we have enough calls or run out of data
      while (filteredCalls.length < limit && iteration < maxIterations) {
        iteration++;

        // Fetch a larger batch to account for office hours filtering
        // Use a multiplier based on expected filter ratio (assume ~50% will be filtered out)
        const batchSize = Math.max(limit * 2, 50);

        logger.debug(
          {
            context: 'fetchAndFilterCallsWithOfficeHours',
            iteration,
            currentOffset,
            batchSize,
            filteredCallsCount: filteredCalls.length,
          },
          'Fetching batch for office hours filtering',
        );

        const queryBuilder = this.buildCallsQuery({
          ...params,
          limit: batchSize + 1, // Get one extra to check for more results
          offset: currentOffset,
        });

        const results = await mysqlService.query<Record<string, unknown>>(
          queryBuilder.sql,
          queryBuilder.params,
        );

        if (results.length === 0) {
          // No more results available
          hasMore = false;
          break;
        }

        // Convert results to Call entities
        const batchCalls = results.slice(0, batchSize).map(row => this.mysqlRowToEntity(row));
        const batchHasMore = results.length > batchSize;

        // Apply office hours filtering to this batch
        const batchFilteredCalls = await this.applyOfficeHoursFiltering(
          batchCalls,
          params.clinicId,
          params.locationId,
        );

        logger.debug(
          {
            context: 'fetchAndFilterCallsWithOfficeHours',
            iteration,
            batchSize: batchCalls.length,
            filteredSize: batchFilteredCalls.length,
            batchHasMore,
          },
          'Batch filtering completed',
        );

        // Add filtered calls to our result set - but avoid duplicates
        const existingIds = new Set(filteredCalls.map(c => c.id));
        const newFilteredCalls = batchFilteredCalls.filter(call => !existingIds.has(call.id));
        filteredCalls.push(...newFilteredCalls);

        // Update offset for next iteration
        currentOffset += batchCalls.length;

        // If this batch had more results and we still need more calls, continue
        if (!batchHasMore) {
          hasMore = false;
          break;
        }

        // If we have enough calls, check if there might be more by trying one more small fetch
        if (filteredCalls.length >= limit) {
          hasMore = batchHasMore; // If the last batch had more, assume we have more filtered results
          break;
        }
      }

      // Trim to requested limit
      const finalCalls = filteredCalls.slice(0, limit);

      // Determine if there are more results
      if (filteredCalls.length > limit) {
        hasMore = true;
      }

      const lastId = finalCalls.length > 0 ? finalCalls[finalCalls.length - 1].id : undefined;

      logger.info(
        {
          context: 'fetchAndFilterCallsWithOfficeHours',
          iterations: iteration,
          totalFetchedBeforeFilter: currentOffset - offset,
          finalCallsCount: finalCalls.length,
          hasMore,
          duration: Date.now() - startTime,
        },
        'Office hours filtered call fetching completed',
      );

      return {
        calls: finalCalls,
        hasMore,
        lastId,
      };
    } catch (error) {
      logger.error(
        {
          context: 'fetchAndFilterCallsWithOfficeHours',
          error: error instanceof Error ? error.message : String(error),
          duration: Date.now() - startTime,
        },
        'Office hours filtered call fetching failed',
      );
      throw error;
    }
  }
}
