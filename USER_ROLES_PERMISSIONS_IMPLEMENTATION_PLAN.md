# User Roles and Permissions Implementation Plan

## Epic Overview
Implementation of a comprehensive user roles and permissions system for the Front Desk Staff Portal, enabling fine-grained access control to features and locations.

## Key Features
- **Enhanced Role System**: Account Owner, Practice Manager, Doctor, Staff roles
- **Feature-Level Permissions**: Control access to Billing, Answering Service, Staff Management, Call Logs, Dashboard, No-Show, Bulk SMS
- **Location-Based Access**: Users can be assigned to specific locations
- **Role Cloning**: Ability to clone existing roles and create custom roles
- **User Invitation System**: Invite users with specific roles and location assignments
- **Admin Panel**: Manage account owners and users across the system

## Permission Features
### Features to Control:
1. **BILLING** - View/manage billing information
2. **ANSWERING_SERVICE** - Access answering service features  
3. **STAFF_MANAGEMENT** - Manage staff members and roles
4. **CALL_LOGS** - View call logs and recordings
5. **DASHBOARD** - Access dashboard analytics
6. **NO_SHOW** - Manage no-show appointments
7. **BULK_SMS** - Send bulk SMS messages
8. **PRACTICE_MANAGEMENT** - Manage practices and locations
9. **USER_MANAGEMENT** - Invite and manage users
10. **ADMIN_TOOLS** - Access admin-only features

### Default Role Permissions:
- **Account Owner**: ALL features (including BILLING)
- **Practice Manager**: ALL features except BILLING
- **Doctor**: CALL_LOGS, DASHBOARD, NO_SHOW
- **Staff**: CALL_LOGS, DASHBOARD (location-specific only)

### Permission Levels:
- **NONE** - No access
- **READ** - View only
- **WRITE** - Create/Edit
- **ADMIN** - Full control including delete

---

## Implementation Plan

## Phase 1: Database Schema & Models (Foundation)

### Database Schema Updates
- [ ] Create new database schema v4 with roles, permissions, and invitations tables
- [ ] Add migration scripts for existing data
- [ ] Update User table to support new role system

### Model Updates
- [ ] Update `models/auth.ts` with new Permission, Role, and UserInvitation interfaces
- [ ] Create feature enum for permission system
- [ ] Update User interface with enhanced role and permission fields
- [ ] Create role template definitions

### Repository Layer
- [ ] Create `lib/repositories/roles-repository.ts`
- [ ] Create `lib/repositories/permissions-repository.ts`
- [ ] Create `lib/repositories/user-invitations-repository.ts`
- [ ] Update `lib/repositories/users-repository.ts` for new role system

## Phase 2: Core Permission System (Backend Logic)

### Permission Services
- [ ] Create `lib/services/permissionService.ts` for permission checking logic
- [ ] Create `lib/services/roleService.ts` for role management
- [ ] Create `utils/permission-utils.ts` for permission helper functions
- [ ] Create `utils/role-templates.ts` with default role definitions

### Middleware & Auth
- [ ] Create `lib/middleware/permissionMiddleware.ts` for API route protection
- [ ] Update `utils/firebase-admin.ts` with permission checking functions
- [ ] Create permission-aware API middleware
- [ ] Update existing auth middleware to include permissions

## Phase 3: API Endpoints (Backend Routes)

### Role Management APIs
- [ ] Create `pages/api/roles/index.ts` (GET, POST for roles)
- [ ] Create `pages/api/roles/[id].ts` (GET, PUT, DELETE specific role)
- [ ] Create `pages/api/roles/clone.ts` (POST to clone existing role)
- [ ] Create `pages/api/roles/templates.ts` (GET default role templates)

### Permission Management APIs
- [ ] Create `pages/api/permissions/index.ts` (GET all permissions)
- [ ] Create `pages/api/permissions/check.ts` (POST to check user permissions)
- [ ] Create `pages/api/users/[id]/permissions.ts` (GET, PUT user permissions)

### User Invitation APIs
- [ ] Create `pages/api/invitations/index.ts` (GET, POST invitations)
- [ ] Create `pages/api/invitations/[id].ts` (GET, PUT, DELETE specific invitation)
- [ ] Create `pages/api/invitations/send.ts` (POST to send invitation email)
- [ ] Create `pages/api/invitations/accept.ts` (POST to accept invitation)
- [ ] Create `pages/api/invitations/resend.ts` (POST to resend invitation)

### Admin APIs
- [ ] Create `pages/api/admin/users.ts` (Admin user management)
- [ ] Create `pages/api/admin/account-owners.ts` (Manage account owners)
- [ ] Update existing `pages/api/staff/` endpoints with new permission system

## Phase 4: User Invitation System (User Onboarding)

### Invitation Service
- [ ] Create `lib/services/userInvitationService.ts`
- [ ] Implement email invitation templates
- [ ] Create invitation token generation and validation
- [ ] Implement invitation expiration logic

### Invitation Flow
- [ ] Create invitation acceptance page
- [ ] Update user registration to handle invitations
- [ ] Implement role and location assignment during acceptance
- [ ] Create invitation status tracking

## Phase 5: UI Components (Frontend Interface)

### Core Permission Components
- [ ] Create `components/PermissionCheck.tsx` (Wrapper for permission-based rendering)
- [ ] Create `components/RoleSelector.tsx` (Role selection dropdown)
- [ ] Create `components/PermissionMatrix.tsx` (Permission grid display)
- [ ] Create `components/LocationSelector.tsx` (Multi-location selection)

### Role Management Interface
- [ ] Create `components/RoleManagement/RoleList.tsx`
- [ ] Create `components/RoleManagement/RoleForm.tsx`
- [ ] Create `components/RoleManagement/RoleClone.tsx`
- [ ] Create `components/RoleManagement/PermissionEditor.tsx`

### User Invitation Interface
- [ ] Create `components/UserInvitation/InviteForm.tsx`
- [ ] Create `components/UserInvitation/InvitationList.tsx`
- [ ] Create `components/UserInvitation/InvitationStatus.tsx`
- [ ] Create `components/UserInvitation/RoleLocationAssignment.tsx`

### Dashboard Pages
- [ ] Create `pages/dashboard/roles/index.tsx` (Role management page)
- [ ] Create `pages/dashboard/roles/[id].tsx` (Edit specific role)
- [ ] Create `pages/dashboard/roles/create.tsx` (Create new role)
- [ ] Create `pages/dashboard/admin/account-owners.tsx` (Admin panel)
- [ ] Create `pages/dashboard/admin/users.tsx` (User management)

### Updated Staff Management
- [ ] Update `pages/dashboard/staff/index.tsx` with new role system
- [ ] Update `pages/dashboard/staff/invite.tsx` with role/location assignment
- [ ] Update `pages/dashboard/staff/[id].tsx` with permission management
- [ ] Create `pages/dashboard/staff/bulk-assign.tsx` (Bulk user assignment)

## Phase 6: Feature Access Control (Permission Integration)

### Navigation & Sidebar Updates
- [ ] Update `components/Sidebar.tsx` with permission-based menu items
- [ ] Update `components/DashboardLayout.tsx` with permission checks
- [ ] Create permission-aware navigation components
- [ ] Update breadcrumb components with access control

### Feature-Specific Access Control
- [ ] Update `pages/dashboard/calls/` with CALL_LOGS permission checks
- [ ] Update `pages/dashboard/index.tsx` with DASHBOARD permission checks
- [ ] Update `pages/dashboard/no-show/` with NO_SHOW permission checks
- [ ] Update `pages/dashboard/admin/bulk-sms.tsx` with BULK_SMS permission checks
- [ ] Create `pages/dashboard/billing/` with BILLING permission checks
- [ ] Update `pages/dashboard/practices/` with PRACTICE_MANAGEMENT permission checks

### Existing Page Updates
- [ ] Update all dashboard pages with PermissionCheck wrapper
- [ ] Add permission-based button/action visibility
- [ ] Update API calls to include permission validation
- [ ] Add permission-based error handling and messaging

## Phase 7: Testing & Validation (Quality Assurance)

### Unit Tests
- [ ] Create tests for permission utilities
- [ ] Create tests for role management service
- [ ] Create tests for invitation service
- [ ] Create tests for permission middleware

### Integration Tests
- [ ] Test role assignment and permission inheritance
- [ ] Test invitation flow end-to-end
- [ ] Test permission-based API access
- [ ] Test location-based access control

### UI Tests
- [ ] Test permission-based component rendering
- [ ] Test role management interface
- [ ] Test invitation interface
- [ ] Test admin panel functionality

## Phase 8: Documentation & Deployment (Finalization)

### Documentation
- [ ] Update README.md with new role system
- [ ] Create API documentation for new endpoints
- [ ] Create user guide for role management
- [ ] Create admin guide for user management

### Migration & Deployment
- [ ] Create data migration scripts for existing users
- [ ] Test migration on staging environment
- [ ] Deploy database schema updates
- [ ] Deploy application updates
- [ ] Verify all features work correctly in production

---

## Key Technical Considerations

### Database Design
- Roles table with customizable permissions
- User-Role assignments with location scoping
- Permission inheritance and override system
- Invitation tracking with expiration

### Security
- Permission checks at both API and UI levels
- Location-based data isolation
- Secure invitation token generation
- Audit logging for role changes

### User Experience
- Intuitive role management interface
- Clear permission visualization
- Smooth invitation acceptance flow
- Responsive design for all screen sizes

### Performance
- Efficient permission checking with caching
- Optimized database queries for role lookups
- Minimal impact on existing functionality
- Scalable architecture for future growth

---

## Success Criteria
- [ ] All default roles work as specified
- [ ] Users can be invited with proper role/location assignment
- [ ] Feature access is properly controlled based on permissions
- [ ] Admin panel allows management of account owners
- [ ] Role cloning functionality works correctly
- [ ] Location-based access control is enforced
- [ ] All existing functionality continues to work
- [ ] Performance impact is minimal
- [ ] Security is maintained throughout the system
- [ ] User experience is intuitive and efficient

## Estimated Timeline
- **Phase 1-2**: 2-3 weeks (Foundation & Core Logic)
- **Phase 3-4**: 2-3 weeks (APIs & Invitation System)
- **Phase 5-6**: 3-4 weeks (UI & Feature Integration)
- **Phase 7-8**: 1-2 weeks (Testing & Deployment)

**Total Estimated Time**: 8-12 weeks

This plan provides a comprehensive roadmap for implementing the User Roles and Permissions system with proper tracking and validation at each phase.
